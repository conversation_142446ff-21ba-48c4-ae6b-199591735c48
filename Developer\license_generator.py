#!/usr/bin/env python3
"""
License Generator Tool for Developers
Creates license.key files for customers
"""

import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import hashlib
import json
import base64
import os
from datetime import datetime

# Configuration - Must match main application
SECRET_KEY = "SecureKey2024!@#$%"  # Same secret as in main.py

def create_signature(email, password, hwid):
    """Create SHA256 signature for license verification"""
    data = f"{email}{password}{hwid}{SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()

def generate_license_file(email, password, hwid, output_path="license.key"):
    """Generate encrypted license file"""
    try:
        # Create signature
        signature = create_signature(email, password, hwid)
        
        # Create license data
        license_data = {
            "email": email,
            "hwid": hwid,
            "signature": signature,
            "created_date": datetime.now().isoformat(),
            "version": "1.0"
        }
        
        # Convert to JSON and encode with base64
        json_data = json.dumps(license_data, indent=None, separators=(',', ':'))
        encoded_data = base64.b64encode(json_data.encode()).decode()
        
        # Save license file
        with open(output_path, "w") as f:
            f.write(encoded_data)
        
        return True, f"License file created successfully: {output_path}"
    
    except Exception as e:
        return False, f"Error creating license: {str(e)}"

def verify_license_file(file_path):
    """Verify a license file for testing"""
    try:
        with open(file_path, "r") as f:
            encoded_data = f.read().strip()
        
        decoded_data = base64.b64decode(encoded_data).decode()
        license_data = json.loads(decoded_data)
        
        return True, license_data
    except Exception as e:
        return False, f"Error reading license: {str(e)}"

class LicenseGeneratorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("License Generator - Developer Tool")
        self.root.geometry("600x700")
        self.root.resizable(False, False)
        self.root.configure(bg='#f5f5f5')
        
        # Center window
        self.center_window()
        
        # Create UI
        self.create_ui()
        
        # License history
        self.license_history = []

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = 600
        height = 700
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_ui(self):
        """Create the user interface"""
        # Header
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🔑 License Generator", 
                font=("Arial", 20, "bold"), bg='#2c3e50', fg='white').pack(pady=20)
        
        tk.Label(header_frame, text="Developer Tool - Create License Files for Customers", 
                font=("Arial", 10), bg='#2c3e50', fg='#bdc3c7').pack()
        
        # Main content
        main_frame = tk.Frame(self.root, bg='#f5f5f5', padx=40, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Customer Information Section
        info_frame = tk.LabelFrame(main_frame, text="📋 Customer Information", 
                                  font=("Arial", 12, "bold"), bg='#ffffff', 
                                  fg='#2c3e50', padx=20, pady=20)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email field
        tk.Label(info_frame, text="Customer Email:", font=("Arial", 11, "bold"), 
                bg='#ffffff', fg='#34495e').grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.email_entry = tk.Entry(info_frame, font=("Arial", 11), width=50, 
                                   relief=tk.SOLID, bd=1)
        self.email_entry.grid(row=1, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(0, 15))
        
        # Password field
        tk.Label(info_frame, text="Customer Password:", font=("Arial", 11, "bold"), 
                bg='#ffffff', fg='#34495e').grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(info_frame, font=("Arial", 11), width=50, 
                                      relief=tk.SOLID, bd=1)
        self.password_entry.grid(row=3, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(0, 15))
        
        # Hardware ID field
        tk.Label(info_frame, text="Customer Hardware ID:", font=("Arial", 11, "bold"), 
                bg='#ffffff', fg='#34495e').grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        
        self.hwid_entry = tk.Entry(info_frame, font=("Arial", 11), width=50, 
                                  relief=tk.SOLID, bd=1)
        self.hwid_entry.grid(row=5, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(0, 5))
        
        tk.Label(info_frame, text="(Customer provides this from their software)", 
                font=("Arial", 9), bg='#ffffff', fg='#7f8c8d').grid(row=6, column=0, sticky=tk.W)
        
        # Configure grid weights
        info_frame.grid_columnconfigure(0, weight=1)
        
        # Actions Section
        actions_frame = tk.LabelFrame(main_frame, text="⚡ Actions", 
                                     font=("Arial", 12, "bold"), bg='#ffffff', 
                                     fg='#2c3e50', padx=20, pady=20)
        actions_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Buttons row 1
        buttons_frame1 = tk.Frame(actions_frame, bg='#ffffff')
        buttons_frame1.pack(fill=tk.X, pady=(0, 10))
        
        tk.Button(buttons_frame1, text="🚀 Generate License", 
                 command=self.generate_license,
                 font=("Arial", 11, "bold"), bg='#27ae60', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=18, pady=8).pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Button(buttons_frame1, text="💾 Save As...", 
                 command=self.save_as_license,
                 font=("Arial", 11, "bold"), bg='#3498db', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=15, pady=8).pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Button(buttons_frame1, text="🗑️ Clear Fields", 
                 command=self.clear_fields,
                 font=("Arial", 11, "bold"), bg='#e74c3c', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=15, pady=8).pack(side=tk.LEFT)
        
        # Buttons row 2
        buttons_frame2 = tk.Frame(actions_frame, bg='#ffffff')
        buttons_frame2.pack(fill=tk.X)
        
        tk.Button(buttons_frame2, text="🔍 Verify License", 
                 command=self.verify_license,
                 font=("Arial", 11, "bold"), bg='#f39c12', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=18, pady=8).pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Button(buttons_frame2, text="📋 Load Template", 
                 command=self.load_template,
                 font=("Arial", 11, "bold"), bg='#9b59b6', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=15, pady=8).pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Button(buttons_frame2, text="📊 Show History", 
                 command=self.show_history,
                 font=("Arial", 11, "bold"), bg='#34495e', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=15, pady=8).pack(side=tk.LEFT)
        
        # Instructions Section
        instructions_frame = tk.LabelFrame(main_frame, text="📖 Instructions", 
                                          font=("Arial", 12, "bold"), bg='#ecf0f1', 
                                          fg='#2c3e50', padx=20, pady=15)
        instructions_frame.pack(fill=tk.X, pady=(0, 20))
        
        instructions_text = """🔹 Customer runs the software and provides their Hardware ID
🔹 Enter customer's email, password, and Hardware ID above
🔹 Click "Generate License" to create license.key file
🔹 Send the license.key file to the customer
🔹 Customer places the file next to their software executable
🔹 Software will activate automatically on next run"""
        
        tk.Label(instructions_frame, text=instructions_text, 
                font=("Arial", 10), bg='#ecf0f1', fg='#2c3e50', 
                justify=tk.LEFT).pack(anchor=tk.W)
        
        # Status Section
        status_frame = tk.Frame(main_frame, bg='#f5f5f5')
        status_frame.pack(fill=tk.X)
        
        self.status_var = tk.StringVar(value="Ready to generate licenses...")
        self.status_label = tk.Label(status_frame, textvariable=self.status_var,
                                    font=("Arial", 10), bg='#f5f5f5', fg='#7f8c8d')
        self.status_label.pack(anchor=tk.W)

    def generate_license(self):
        """Generate license file with default name"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        hwid = self.hwid_entry.get().strip()
        
        if not self.validate_inputs(email, password, hwid):
            return
        
        # Generate license
        success, message = generate_license_file(email, password, hwid)
        
        if success:
            # Add to history
            self.license_history.append({
                "email": email,
                "hwid": hwid,
                "created": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "file": "license.key"
            })
            
            self.status_var.set(f"✅ License created for {email}")
            messagebox.showinfo("Success", f"{message}\n\n📧 Send this file to your customer!")
        else:
            self.status_var.set("❌ License generation failed")
            messagebox.showerror("Error", message)

    def save_as_license(self):
        """Save license file with custom name/location"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        hwid = self.hwid_entry.get().strip()
        
        if not self.validate_inputs(email, password, hwid):
            return
        
        # Ask for save location
        file_path = filedialog.asksaveasfilename(
            title="Save License File",
            defaultextension=".key",
            filetypes=[("License files", "*.key"), ("All files", "*.*")],
            initialvalue=f"license_{email.split('@')[0]}.key"
        )
        
        if file_path:
            success, message = generate_license_file(email, password, hwid, file_path)
            
            if success:
                # Add to history
                self.license_history.append({
                    "email": email,
                    "hwid": hwid,
                    "created": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "file": os.path.basename(file_path)
                })
                
                self.status_var.set(f"✅ License saved: {os.path.basename(file_path)}")
                messagebox.showinfo("Success", f"License saved to:\n{file_path}\n\n📧 Send this file to your customer!")
            else:
                self.status_var.set("❌ License save failed")
                messagebox.showerror("Error", message)

    def verify_license(self):
        """Verify an existing license file"""
        file_path = filedialog.askopenfilename(
            title="Select License File to Verify",
            filetypes=[("License files", "*.key"), ("All files", "*.*")]
        )
        
        if file_path:
            success, result = verify_license_file(file_path)
            
            if success:
                info = f"""✅ License File Verification Successful!

📧 Email: {result.get('email', 'Unknown')}
💻 Hardware ID: {result.get('hwid', 'Unknown')}
📅 Created: {result.get('created_date', 'Unknown')}
🔢 Version: {result.get('version', 'Unknown')}
🔐 Signature: {result.get('signature', 'Unknown')[:16]}..."""
                
                messagebox.showinfo("License Verification", info)
                self.status_var.set("✅ License verification successful")
            else:
                messagebox.showerror("Verification Failed", f"❌ {result}")
                self.status_var.set("❌ License verification failed")

    def load_template(self):
        """Load a template with sample data"""
        self.email_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.hwid_entry.delete(0, tk.END)
        
        self.email_entry.insert(0, "<EMAIL>")
        self.password_entry.insert(0, "password123")
        self.hwid_entry.insert(0, "ABCD-1234-EFGH-5678")
        
        self.status_var.set("📋 Template loaded - modify as needed")

    def show_history(self):
        """Show license generation history"""
        if not self.license_history:
            messagebox.showinfo("History", "No licenses generated yet.")
            return
        
        history_window = tk.Toplevel(self.root)
        history_window.title("License Generation History")
        history_window.geometry("500x400")
        history_window.configure(bg='#f5f5f5')
        
        # History list
        frame = tk.Frame(history_window, bg='#f5f5f5', padx=20, pady=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(frame, text="📊 License Generation History", 
                font=("Arial", 14, "bold"), bg='#f5f5f5', fg='#2c3e50').pack(pady=(0, 20))
        
        # Create treeview
        tree = ttk.Treeview(frame, columns=('Email', 'HWID', 'Created', 'File'), show='headings')
        tree.heading('Email', text='Email')
        tree.heading('HWID', text='Hardware ID')
        tree.heading('Created', text='Created')
        tree.heading('File', text='File')
        
        # Add data
        for item in self.license_history:
            tree.insert('', tk.END, values=(
                item['email'], 
                item['hwid'][:16] + '...', 
                item['created'], 
                item['file']
            ))
        
        tree.pack(fill=tk.BOTH, expand=True)

    def validate_inputs(self, email, password, hwid):
        """Validate input fields"""
        if not email or not password or not hwid:
            messagebox.showerror("Error", "Please fill in all fields")
            self.status_var.set("❌ Missing required fields")
            return False
        
        if "@" not in email:
            messagebox.showerror("Error", "Please enter a valid email address")
            self.status_var.set("❌ Invalid email format")
            return False
        
        if len(hwid) < 10:
            messagebox.showerror("Error", "Hardware ID seems too short")
            self.status_var.set("❌ Invalid hardware ID")
            return False
        
        return True

    def clear_fields(self):
        """Clear all input fields"""
        self.email_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.hwid_entry.delete(0, tk.END)
        self.status_var.set("🗑️ Fields cleared")

if __name__ == "__main__":
    root = tk.Tk()
    app = LicenseGeneratorApp(root)
    root.mainloop()
