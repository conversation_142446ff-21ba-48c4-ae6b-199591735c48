#!/usr/bin/env python3
"""
License Generator for Developers
Creates license.key files for customers
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import hashlib
import json
import base64
import os

# Configuration - Must match main.py
SECRET_KEY = "MySecretKey2024!@#"  # Same secret as in main.py

def create_signature(email, password, hwid):
    """Create SHA256 signature"""
    data = f"{email}{password}{hwid}{SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()

def generate_license_file(email, password, hwid, output_path="license.key"):
    """Generate license file"""
    try:
        # Create signature
        signature = create_signature(email, password, hwid)
        
        # Create license data
        license_data = {
            "email": email,
            "hwid": hwid,
            "signature": signature
        }
        
        # Encode to base64
        json_data = json.dumps(license_data)
        encoded_data = base64.b64encode(json_data.encode()).decode()
        
        # Save license file
        with open(output_path, "w") as f:
            f.write(encoded_data)
        
        return True, f"License file created: {output_path}"
    
    except Exception as e:
        return False, f"Error creating license: {str(e)}"

class LicenseGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("License Generator - Developer Tool")
        self.root.geometry("500x450")
        self.root.resizable(False, False)
        self.root.configure(bg='#f5f5f5')
        
        # Center window
        self.center_window()
        
        # Create UI
        self.create_ui()

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = 500
        height = 450
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_ui(self):
        """Create the user interface"""
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f5f5f5', padx=40, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="🔑 License Generator", 
                              font=("Arial", 20, "bold"), bg='#f5f5f5', fg='#2c3e50')
        title_label.pack(pady=(0, 10))
        
        subtitle_label = tk.Label(main_frame, text="Developer Tool - Create License Files", 
                                 font=("Arial", 10), bg='#f5f5f5', fg='#7f8c8d')
        subtitle_label.pack(pady=(0, 30))
        
        # Input fields frame
        fields_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1, padx=25, pady=25)
        fields_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email field
        tk.Label(fields_frame, text="Customer Email:", font=("Arial", 12, "bold"), 
                bg='#ffffff', fg='#2c3e50').pack(anchor=tk.W, pady=(0, 5))
        
        self.email_entry = tk.Entry(fields_frame, font=("Arial", 11), width=45, 
                                   relief=tk.SOLID, bd=1)
        self.email_entry.pack(pady=(0, 15), ipady=5)
        
        # Password field
        tk.Label(fields_frame, text="Customer Password:", font=("Arial", 12, "bold"), 
                bg='#ffffff', fg='#2c3e50').pack(anchor=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(fields_frame, font=("Arial", 11), width=45, 
                                      relief=tk.SOLID, bd=1)
        self.password_entry.pack(pady=(0, 15), ipady=5)
        
        # Hardware ID field
        tk.Label(fields_frame, text="Customer Hardware ID:", font=("Arial", 12, "bold"), 
                bg='#ffffff', fg='#2c3e50').pack(anchor=tk.W, pady=(0, 5))
        
        self.hwid_entry = tk.Entry(fields_frame, font=("Arial", 11), width=45, 
                                  relief=tk.SOLID, bd=1)
        self.hwid_entry.pack(pady=(0, 5), ipady=5)
        
        tk.Label(fields_frame, text="(Customer sends this from their software)", 
                font=("Arial", 8), bg='#ffffff', fg='#95a5a6').pack(anchor=tk.W)
        
        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(pady=20)
        
        # Generate button
        generate_btn = tk.Button(buttons_frame, text="🚀 Generate License", 
                               command=self.generate_license,
                               font=("Arial", 12, "bold"), bg='#27ae60', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=20, pady=8)
        generate_btn.pack(side=tk.LEFT, padx=5)
        
        # Save As button
        save_as_btn = tk.Button(buttons_frame, text="💾 Save As...", 
                              command=self.save_as_license,
                              font=("Arial", 12, "bold"), bg='#3498db', fg='white',
                              relief=tk.FLAT, cursor="hand2", width=15, pady=8)
        save_as_btn.pack(side=tk.LEFT, padx=5)
        
        # Clear button
        clear_btn = tk.Button(buttons_frame, text="🗑️ Clear", 
                            command=self.clear_fields,
                            font=("Arial", 12, "bold"), bg='#e74c3c', fg='white',
                            relief=tk.FLAT, cursor="hand2", width=10, pady=8)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # Instructions
        instructions_frame = tk.Frame(main_frame, bg='#ecf0f1', relief=tk.SOLID, bd=1, padx=15, pady=15)
        instructions_frame.pack(fill=tk.X, pady=(20, 0))
        
        instructions_text = """📋 Instructions:
1. Customer runs the software and sends you their Hardware ID
2. Enter customer's email, password, and Hardware ID above
3. Click "Generate License" to create license.key
4. Send the license.key file to the customer
5. Customer places the file next to the software executable"""
        
        tk.Label(instructions_frame, text=instructions_text, 
                font=("Arial", 9), bg='#ecf0f1', fg='#2c3e50', 
                justify=tk.LEFT).pack(anchor=tk.W)

    def generate_license(self):
        """Generate license file"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        hwid = self.hwid_entry.get().strip()
        
        # Validate inputs
        if not email or not password or not hwid:
            messagebox.showerror("Error", "Please fill in all fields")
            return
        
        if "@" not in email:
            messagebox.showerror("Error", "Please enter a valid email address")
            return
        
        # Generate license
        success, message = generate_license_file(email, password, hwid)
        
        if success:
            messagebox.showinfo("Success", f"{message}\n\nSend this file to your customer!")
        else:
            messagebox.showerror("Error", message)

    def save_as_license(self):
        """Save license file with custom name/location"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        hwid = self.hwid_entry.get().strip()
        
        # Validate inputs
        if not email or not password or not hwid:
            messagebox.showerror("Error", "Please fill in all fields")
            return
        
        if "@" not in email:
            messagebox.showerror("Error", "Please enter a valid email address")
            return
        
        # Ask for save location
        file_path = filedialog.asksaveasfilename(
            title="Save License File",
            defaultextension=".key",
            filetypes=[("License files", "*.key"), ("All files", "*.*")],
            initialvalue="license.key"
        )
        
        if file_path:
            success, message = generate_license_file(email, password, hwid, file_path)
            
            if success:
                messagebox.showinfo("Success", f"License saved to:\n{file_path}\n\nSend this file to your customer!")
            else:
                messagebox.showerror("Error", message)

    def clear_fields(self):
        """Clear all input fields"""
        self.email_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.hwid_entry.delete(0, tk.END)

if __name__ == "__main__":
    root = tk.Tk()
    app = LicenseGenerator(root)
    root.mainloop()
