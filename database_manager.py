#!/usr/bin/env python3
"""
Database Manager for User Management with SQLite
Creates and manages encrypted SQLite database for user activation
"""

import sqlite3
import hashlib
import json
import os
import base64
from cryptography.fernet import Fernet
import uuid
import platform
import subprocess
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_file="users.db", key_file="db.key"):
        self.db_file = db_file
        self.key_file = key_file
        self.secret_key = self.load_or_create_key()
        self.fernet = Fernet(self.secret_key)
        self.init_database()
    
    def load_or_create_key(self):
        """Load or create encryption key"""
        if os.path.exists(self.key_file):
            with open(self.key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            print(f"✅ Created new encryption key: {self.key_file}")
            return key
    
    def get_hardware_id(self):
        """Get unique hardware ID"""
        try:
            system_info = platform.system() + platform.version() + platform.machine()
            
            if platform.system() == "Windows":
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            else:
                system_info += str(uuid.getnode())
            
            hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
            formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
            return formatted_id.upper()
        except:
            node = str(uuid.getnode())
            return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()
    
    def encrypt_data(self, data):
        """Encrypt data"""
        return self.fernet.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data):
        """Decrypt data"""
        return self.fernet.decrypt(encrypted_data.encode()).decode()
    
    def init_database(self):
        """Initialize database with users table"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # Create users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                hwid TEXT NOT NULL,
                created_date TEXT NOT NULL,
                last_login TEXT,
                status TEXT DEFAULT 'active',
                notes TEXT
            )
        ''')
        
        # Create licenses table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                hwid TEXT NOT NULL,
                license_key TEXT NOT NULL,
                created_date TEXT NOT NULL,
                status TEXT DEFAULT 'active',
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Database initialized successfully")
    
    def hash_password(self, password):
        """Hash password with salt"""
        salt = "activation_system_salt_2024"
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    def add_user(self, email, password, hwid, notes=""):
        """Add new user to database"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            created_date = datetime.now().isoformat()
            
            cursor.execute('''
                INSERT INTO users (email, password_hash, hwid, created_date, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (email, password_hash, hwid, created_date, notes))
            
            conn.commit()
            user_id = cursor.lastrowid
            conn.close()
            
            print(f"✅ User added successfully: {email}")
            return user_id
        except sqlite3.IntegrityError:
            print(f"❌ User already exists: {email}")
            return None
        except Exception as e:
            print(f"❌ Error adding user: {e}")
            return None
    
    def validate_user(self, email, password, hwid):
        """Validate user credentials"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                SELECT id, email, status FROM users 
                WHERE email = ? AND password_hash = ? AND hwid = ? AND status = 'active'
            ''', (email, password_hash, hwid))
            
            result = cursor.fetchone()
            
            if result:
                # Update last login
                cursor.execute('''
                    UPDATE users SET last_login = ? WHERE id = ?
                ''', (datetime.now().isoformat(), result[0]))
                conn.commit()
                
                conn.close()
                print(f"✅ User validation successful: {email}")
                return True
            else:
                conn.close()
                print(f"❌ User validation failed: {email}")
                return False
                
        except Exception as e:
            print(f"❌ Error validating user: {e}")
            return False
    
    def list_users(self):
        """List all users"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, email, hwid, created_date, last_login, status, notes
                FROM users ORDER BY created_date DESC
            ''')
            
            users = cursor.fetchall()
            conn.close()
            
            return users
        except Exception as e:
            print(f"❌ Error listing users: {e}")
            return []
    
    def delete_user(self, email):
        """Delete user by email"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM users WHERE email = ?', (email,))
            
            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                print(f"✅ User deleted: {email}")
                return True
            else:
                conn.close()
                print(f"❌ User not found: {email}")
                return False
                
        except Exception as e:
            print(f"❌ Error deleting user: {e}")
            return False
    
    def update_user_status(self, email, status):
        """Update user status"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('UPDATE users SET status = ? WHERE email = ?', (status, email))
            
            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                print(f"✅ User status updated: {email} -> {status}")
                return True
            else:
                conn.close()
                print(f"❌ User not found: {email}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating user status: {e}")
            return False
    
    def encrypt_database(self, output_file="users_encrypted.db"):
        """Encrypt entire database for distribution"""
        try:
            # Read database file
            with open(self.db_file, 'rb') as f:
                db_data = f.read()
            
            # Encrypt database
            encrypted_data = self.fernet.encrypt(db_data)
            
            # Save encrypted database
            with open(output_file, 'wb') as f:
                f.write(encrypted_data)
            
            print(f"✅ Database encrypted successfully: {output_file}")
            return True
        except Exception as e:
            print(f"❌ Error encrypting database: {e}")
            return False
    
    def decrypt_database(self, encrypted_file, output_file="users_decrypted.db"):
        """Decrypt database file"""
        try:
            # Read encrypted database
            with open(encrypted_file, 'rb') as f:
                encrypted_data = f.read()
            
            # Decrypt database
            db_data = self.fernet.decrypt(encrypted_data)
            
            # Save decrypted database
            with open(output_file, 'wb') as f:
                f.write(db_data)
            
            print(f"✅ Database decrypted successfully: {output_file}")
            return True
        except Exception as e:
            print(f"❌ Error decrypting database: {e}")
            return False

def main():
    """Main function for database management"""
    db = DatabaseManager()
    
    while True:
        print("\n" + "="*50)
        print("🗄️  DATABASE MANAGER")
        print("="*50)
        print("1. Add User")
        print("2. List Users") 
        print("3. Validate User")
        print("4. Delete User")
        print("5. Update User Status")
        print("6. Encrypt Database")
        print("7. Show Current Hardware ID")
        print("8. Exit")
        
        choice = input("\nEnter choice (1-8): ").strip()
        
        if choice == "1":
            email = input("Email: ").strip()
            password = input("Password: ").strip()
            hwid = input("Hardware ID: ").strip()
            notes = input("Notes (optional): ").strip()
            db.add_user(email, password, hwid, notes)
            
        elif choice == "2":
            users = db.list_users()
            print(f"\n📋 Users ({len(users)}):")
            for user in users:
                print(f"   ID: {user[0]} | Email: {user[1]} | HWID: {user[2]}")
                print(f"   Created: {user[3]} | Status: {user[5]}")
                if user[6]:
                    print(f"   Notes: {user[6]}")
                print("   " + "-"*40)
                
        elif choice == "3":
            email = input("Email: ").strip()
            password = input("Password: ").strip()
            hwid = input("Hardware ID: ").strip()
            result = db.validate_user(email, password, hwid)
            print(f"Validation result: {'✅ SUCCESS' if result else '❌ FAILED'}")
            
        elif choice == "4":
            email = input("Email to delete: ").strip()
            db.delete_user(email)
            
        elif choice == "5":
            email = input("Email: ").strip()
            status = input("New status (active/inactive): ").strip()
            db.update_user_status(email, status)
            
        elif choice == "6":
            output_file = input("Output file (default: users_encrypted.db): ").strip()
            if not output_file:
                output_file = "users_encrypted.db"
            db.encrypt_database(output_file)
            
        elif choice == "7":
            hwid = db.get_hardware_id()
            print(f"💻 Current Hardware ID: {hwid}")
            
        elif choice == "8":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice!")

if __name__ == "__main__":
    main()
