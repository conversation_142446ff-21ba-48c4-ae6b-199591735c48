#!/usr/bin/env python3
"""
Main Application with Activation System
Requires activation before use
"""

import tkinter as tk
from tkinter import messagebox
import json
import hashlib
import platform
import subprocess
import secrets
import uuid
import os
import webbrowser
import pyperclip
from cryptography.fernet import <PERSON><PERSON><PERSON>
from PIL import Image, ImageTk

class ActivationSystem:
    def __init__(self):
        self.secret_key = b'ShSzsqijTDPxxrYmb5BgHbgI3sFbGSJeRNK6FLpH89s='  # Embedded key
        self.license_file = "license.key"
        self.users_file = "users.enc"
        
    def get_hardware_id(self):
        """Get unique hardware ID for this machine"""
        try:
            # Get system info
            system_info = platform.system() + platform.version() + platform.machine()
            
            # Get UUID from system
            if platform.system() == "Windows":
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            elif platform.system() == "Linux":
                with open('/etc/machine-id', 'r') as f:
                    system_info += f.read().strip()
            elif platform.system() == "Darwin":  # macOS
                result = subprocess.check_output('ioreg -rd1 -c IOPlatformExpertDevice', shell=True).decode()
                for line in result.split('\n'):
                    if 'IOPlatformUUID' in line:
                        system_info += line.split('"')[3]
                        break
        except:
            # Fallback to MAC address based ID
            system_info += str(uuid.getnode())
        
        # Create hash
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        return formatted_id.upper()
    
    def check_license(self):
        """Check if valid license exists"""
        try:
            if not os.path.exists(self.license_file):
                return False
            
            with open(self.license_file, 'r') as f:
                license_hwid = f.read().strip()
            
            current_hwid = self.get_hardware_id()
            return license_hwid == current_hwid
            
        except:
            return False
    
    def load_users(self):
        """Load and decrypt users data"""
        try:
            if not os.path.exists(self.users_file):
                return []
            
            fernet = Fernet(self.secret_key)
            
            with open(self.users_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = fernet.decrypt(encrypted_data)
            users = json.loads(decrypted_data.decode())
            return users
            
        except Exception as e:
            print(f"Error loading users: {e}")
            return []
    
    def validate_user(self, email, password, hwid):
        """Validate user credentials"""
        users = self.load_users()
        
        for user in users:
            if (user['email'].lower() == email.lower() and 
                user['password'] == password and 
                user['hwid'] == hwid):
                return True
        return False
    
    def create_license(self):
        """Create license file for current hardware"""
        try:
            hwid = self.get_hardware_id()
            with open(self.license_file, 'w') as f:
                f.write(hwid)
            return True
        except:
            return False

class LoginApp:
    def __init__(self, root):
        self.root = root
        self.activation = ActivationSystem()
        
        # Check if already activated
        if self.activation.check_license():
            self.show_main_app()
        else:
            self.show_activation_window()
    
    def show_activation_window(self):
        """Show activation window"""
        self.root.title("Software Activation")
        self.root.geometry("500x650")
        self.root.resizable(False, False)
        
        # Set window icon
        try:
            self.root.iconbitmap("logo.ico")
        except:
            pass
        
        # Professional color scheme
        self.colors = {
            'bg_primary': '#1e293b',
            'bg_secondary': '#334155',
            'bg_light': '#f8fafc',
            'accent': '#3b82f6',
            'accent_hover': '#2563eb',
            'text_primary': '#ffffff',
            'text_secondary': '#94a3b8',
            'success': '#10b981',
            'danger': '#ef4444'
        }
        
        self.root.configure(bg=self.colors['bg_primary'])
        self.create_activation_ui()
    
    def create_activation_ui(self):
        """Create activation interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], padx=30, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Logo
        try:
            logo_image = Image.open("logo.ico")
            logo_image = logo_image.resize((80, 80), Image.Resampling.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_image)
            
            logo_label = tk.Label(main_frame, image=self.logo_photo, bg=self.colors['bg_primary'])
            logo_label.pack(pady=(0, 15))
        except:
            pass
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="SOFTWARE ACTIVATION",
            font=("Segoe UI", 18, "bold"),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary']
        )
        title_label.pack(pady=(0, 25))
        
        # Activation form
        form_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], padx=25, pady=25)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email field
        email_label = tk.Label(form_frame, text="Email:", font=("Segoe UI", 11, "bold"),
                              fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
        email_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.email_var = tk.StringVar()
        email_entry = tk.Entry(form_frame, textvariable=self.email_var, font=("Segoe UI", 11),
                              bg=self.colors['bg_light'], fg='#1e293b', relief=tk.FLAT, bd=0,
                              highlightthickness=2, highlightcolor=self.colors['accent'])
        email_entry.pack(fill=tk.X, pady=(0, 15), ipady=8)
        
        # Password field
        password_label = tk.Label(form_frame, text="Password:", font=("Segoe UI", 11, "bold"),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
        password_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.password_var = tk.StringVar()
        password_entry = tk.Entry(form_frame, textvariable=self.password_var, show="*",
                                 font=("Segoe UI", 11), bg=self.colors['bg_light'], fg='#1e293b',
                                 relief=tk.FLAT, bd=0, highlightthickness=2,
                                 highlightcolor=self.colors['accent'])
        password_entry.pack(fill=tk.X, pady=(0, 20), ipady=8)
        
        # Activate button
        activate_button = tk.Button(form_frame, text="ACTIVATE SOFTWARE", command=self.activate,
                                   font=("Segoe UI", 12, "bold"), bg=self.colors['accent'],
                                   fg=self.colors['text_primary'], relief=tk.FLAT, bd=0,
                                   cursor="hand2", activebackground=self.colors['accent_hover'],
                                   activeforeground=self.colors['text_primary'])
        activate_button.pack(fill=tk.X, pady=(0, 10), ipady=10)
        
        # Hardware ID section
        self.create_hardware_section(main_frame)
        
        # Contact section
        self.create_contact_section(main_frame)
    
    def create_hardware_section(self, parent):
        """Create hardware ID section"""
        hw_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], padx=25, pady=20)
        hw_frame.pack(fill=tk.X, pady=(0, 20))
        
        hw_title = tk.Label(hw_frame, text="Hardware ID", font=("Segoe UI", 12, "bold"),
                           fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
        hw_title.pack(pady=(0, 5))
        
        hw_subtitle = tk.Label(hw_frame, text="Send this ID to get activation",
                              font=("Segoe UI", 9), fg=self.colors['text_secondary'],
                              bg=self.colors['bg_secondary'])
        hw_subtitle.pack(pady=(0, 10))
        
        # Hardware ID display
        hw_id_frame = tk.Frame(hw_frame, bg=self.colors['bg_secondary'])
        hw_id_frame.pack(fill=tk.X)
        
        self.hardware_id = self.activation.get_hardware_id()
        self.hw_id_var = tk.StringVar(value=self.hardware_id)
        
        hw_entry = tk.Entry(hw_id_frame, textvariable=self.hw_id_var, state="readonly",
                           font=("Consolas", 10), bg=self.colors['bg_light'], fg='#1e293b',
                           relief=tk.FLAT, bd=0, justify=tk.CENTER)
        hw_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5)
        
        copy_button = tk.Button(hw_id_frame, text="COPY", command=self.copy_hardware_id,
                               font=("Segoe UI", 9, "bold"), bg=self.colors['success'],
                               fg=self.colors['text_primary'], relief=tk.FLAT, bd=0,
                               cursor="hand2", width=8)
        copy_button.pack(side=tk.RIGHT, padx=(10, 0), ipady=5)
    
    def create_contact_section(self, parent):
        """Create contact section"""
        social_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], padx=25, pady=20)
        social_frame.pack(fill=tk.X, pady=(10, 0))
        
        contact_label = tk.Label(social_frame, text="Contact Us", font=("Segoe UI", 12, "bold"),
                                fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
        contact_label.pack(pady=(0, 15))
        
        # Social media buttons
        buttons_frame = tk.Frame(social_frame, bg=self.colors['bg_secondary'])
        buttons_frame.pack()
        
        # WhatsApp
        whatsapp_btn = tk.Button(buttons_frame, text="📱 WhatsApp", command=self.open_whatsapp,
                                font=("Segoe UI", 9, "bold"), bg="#25D366", fg="white",
                                relief=tk.FLAT, bd=0, cursor="hand2", width=12,
                                activebackground="#1DA851", activeforeground="white")
        whatsapp_btn.pack(side=tk.LEFT, padx=5, ipady=6)
        
        # Facebook
        facebook_btn = tk.Button(buttons_frame, text="📘 Facebook", command=self.open_facebook,
                                font=("Segoe UI", 9, "bold"), bg="#1877f2", fg="white",
                                relief=tk.FLAT, bd=0, cursor="hand2", width=12,
                                activebackground="#166fe5", activeforeground="white")
        facebook_btn.pack(side=tk.LEFT, padx=5, ipady=6)
        
        # Telegram
        telegram_btn = tk.Button(buttons_frame, text="✈️ Telegram", command=self.open_telegram,
                                font=("Segoe UI", 9, "bold"), bg="#0088cc", fg="white",
                                relief=tk.FLAT, bd=0, cursor="hand2", width=12,
                                activebackground="#006699", activeforeground="white")
        telegram_btn.pack(side=tk.LEFT, padx=5, ipady=6)
    
    def copy_hardware_id(self):
        """Copy hardware ID to clipboard"""
        pyperclip.copy(self.hardware_id)
        messagebox.showinfo("Copied", "Hardware ID copied to clipboard!")
    
    def open_whatsapp(self):
        webbrowser.open("https://wa.me/201200578402")
    
    def open_facebook(self):
        webbrowser.open("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v")
    
    def open_telegram(self):
        webbrowser.open("http://t.me/Mohamed_Abdo26")
    
    def activate(self):
        """Activate the software"""
        email = self.email_var.get().strip()
        password = self.password_var.get().strip()
        
        if not email or not password:
            messagebox.showerror("Error", "Please enter both email and password")
            return
        
        # Validate credentials
        if self.activation.validate_user(email, password, self.hardware_id):
            if self.activation.create_license():
                messagebox.showinfo("Success", "Software activated successfully!\nRestarting application...")
                self.root.destroy()
                self.restart_app()
            else:
                messagebox.showerror("Error", "Failed to create license file")
        else:
            messagebox.showerror("Error", "Invalid credentials or hardware ID not authorized")
    
    def restart_app(self):
        """Restart the application"""
        import sys
        import subprocess
        subprocess.Popen([sys.executable] + sys.argv)
    
    def show_main_app(self):
        """Show the main application after activation"""
        self.root.title("Activated Software")
        self.root.geometry("600x400")
        
        # Clear the window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Main app interface
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=50, pady=50)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Success message
        success_label = tk.Label(main_frame, text="🎉 SOFTWARE ACTIVATED SUCCESSFULLY! 🎉",
                                font=("Segoe UI", 20, "bold"), fg='#10b981', bg='#f0f0f0')
        success_label.pack(pady=(0, 30))
        
        # Info
        info_label = tk.Label(main_frame, 
                             text="Your software is now fully activated and ready to use.\nThis activation is permanent for this device.",
                             font=("Segoe UI", 12), fg='#374151', bg='#f0f0f0', justify=tk.CENTER)
        info_label.pack(pady=(0, 30))
        
        # Hardware ID display
        hw_info = tk.Label(main_frame, text=f"Licensed Hardware ID: {self.activation.get_hardware_id()}",
                          font=("Consolas", 10), fg='#6b7280', bg='#f0f0f0')
        hw_info.pack(pady=(0, 20))
        
        # Your main application content goes here
        content_label = tk.Label(main_frame, text="🚀 Your Main Application Content Here 🚀",
                                font=("Segoe UI", 16, "bold"), fg='#3b82f6', bg='#f0f0f0')
        content_label.pack(pady=20)

if __name__ == "__main__":
    root = tk.Tk()
    
    # Center window
    root.update_idletasks()
    width = 500
    height = 650
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    app = LoginApp(root)
    root.mainloop()
