#!/usr/bin/env python3
"""
Test script for the activation system
"""

from cryptography.fernet import Fernet
import json
import os

def test_encryption():
    """Test encryption/decryption"""
    print("🔐 Testing Encryption System...")
    
    try:
        # Load key
        with open('secret.key', 'rb') as f:
            key = f.read()
        
        fernet = Fernet(key)
        
        # Test decrypt users.enc
        with open('users.enc', 'rb') as f:
            encrypted_data = f.read()
        
        decrypted_data = fernet.decrypt(encrypted_data)
        users = json.loads(decrypted_data.decode())
        
        print("✅ Encryption/Decryption working!")
        print(f"✅ Found {len(users)} users in database")
        
        for i, user in enumerate(users, 1):
            print(f"   {i}. {user['email']} | HWID: {user['hwid']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        return False

def test_files():
    """Test required files exist"""
    print("\n📁 Testing Required Files...")
    
    required_files = [
        'main.py',
        'users.json', 
        'users.enc',
        'secret.key',
        'logo.ico'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING!")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ All required files present!")
        return True

def test_exe():
    """Test if EXE exists"""
    print("\n🚀 Testing EXE File...")
    
    exe_path = "dist/main.exe"
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path) / (1024*1024)  # MB
        print(f"✅ main.exe exists ({size:.1f} MB)")
        return True
    else:
        print("❌ main.exe not found in dist/ folder")
        print("   Run: pyinstaller --onefile --windowed --icon=logo.ico --add-data \"users.enc;.\" main.py")
        return False

def main():
    """Run all tests"""
    print("🧪 ACTIVATION SYSTEM TEST")
    print("=" * 40)
    
    tests = [
        test_files(),
        test_encryption(),
        test_exe()
    ]
    
    print("\n" + "=" * 40)
    if all(tests):
        print("🎉 ALL TESTS PASSED!")
        print("\n📦 Ready for distribution:")
        print("   - dist/main.exe")
        print("   - users.enc")
        print("\n🔒 Keep secret:")
        print("   - secret.key")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please fix the issues above.")

if __name__ == "__main__":
    main()
