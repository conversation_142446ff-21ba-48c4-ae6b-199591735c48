# Requirements for License Generator Tool
# No external dependencies required - uses only Python standard library

# Python Standard Library modules used:
# - tkinter (GUI framework)
# - tkinter.ttk (themed widgets)
# - tkinter.messagebox (message dialogs)
# - tkinter.filedialog (file dialogs)
# - hashlib (cryptographic hashing)
# - json (JSON data handling)
# - base64 (base64 encoding/decoding)
# - os (operating system interface)
# - datetime (date and time handling)

# Minimum Python version: 3.6+
