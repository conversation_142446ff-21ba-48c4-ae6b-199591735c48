#!/usr/bin/env python3
"""
Software Activation Program
Separate activation interface for user registration and license generation
"""

import tkinter as tk
from tkinter import messagebox
import sqlite3
import hashlib
import os
import json
from cryptography.fernet import Fernet
import uuid
import platform
import subprocess
import webbrowser
import pyperclip
from PIL import Image, ImageTk
from datetime import datetime

# Configuration
LICENSE_FILE = "license.key"
ENCRYPTED_DB_FILE = "users_encrypted.db"
SECRET_KEY = b'4ibKLILdcFR7E--NU8rH2eFBf90nOSDwwjnHBk2RO00='  # Embedded key

class ActivationSystem:
    def __init__(self):
        self.fernet = Fernet(SECRET_KEY)
        self.temp_db = "temp_activation.db"
        
    def get_hardware_id(self):
        """Get unique hardware ID"""
        try:
            system_info = platform.system() + platform.version() + platform.machine()
            
            if platform.system() == "Windows":
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            else:
                system_info += str(uuid.getnode())
            
            hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
            formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
            return formatted_id.upper()
        except:
            node = str(uuid.getnode())
            return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()
    
    def decrypt_database(self):
        """Decrypt database for temporary use"""
        try:
            if not os.path.exists(ENCRYPTED_DB_FILE):
                print(f"❌ Encrypted database not found: {ENCRYPTED_DB_FILE}")
                return False
            
            with open(ENCRYPTED_DB_FILE, 'rb') as f:
                encrypted_data = f.read()
            
            db_data = self.fernet.decrypt(encrypted_data)
            
            with open(self.temp_db, 'wb') as f:
                f.write(db_data)
            
            return True
        except Exception as e:
            print(f"❌ Error decrypting database: {e}")
            return False
    
    def cleanup_temp_db(self):
        """Remove temporary database file"""
        try:
            if os.path.exists(self.temp_db):
                os.remove(self.temp_db)
        except:
            pass
    
    def hash_password(self, password):
        """Hash password with salt"""
        salt = "activation_system_salt_2024"
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    def validate_user(self, email, password, hwid):
        """Validate user credentials against database"""
        try:
            if not self.decrypt_database():
                return False
            
            conn = sqlite3.connect(self.temp_db)
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                SELECT id, email, status FROM users 
                WHERE email = ? AND password_hash = ? AND hwid = ? AND status = 'active'
            ''', (email, password_hash, hwid))
            
            result = cursor.fetchone()
            
            if result:
                # Update last login
                cursor.execute('''
                    UPDATE users SET last_login = ? WHERE id = ?
                ''', (datetime.now().isoformat(), result[0]))
                conn.commit()
            
            conn.close()
            self.cleanup_temp_db()
            
            return result is not None
                
        except Exception as e:
            print(f"❌ Error validating user: {e}")
            self.cleanup_temp_db()
            return False
    
    def save_license(self):
        """Save license file"""
        try:
            hwid = self.get_hardware_id()
            license_data = {
                "hardware_id": hwid,
                "activated_date": datetime.now().isoformat(),
                "status": "activated"
            }
            
            with open(LICENSE_FILE, "w") as f:
                json.dump(license_data, f, indent=2)
            
            print(f"✅ License saved for Hardware ID: {hwid}")
            return True
        except Exception as e:
            print(f"❌ Error saving license: {e}")
            return False

class ActivationApp:
    def __init__(self, root):
        self.root = root
        self.activation = ActivationSystem()
        self.root.title("Software Activation")
        self.root.geometry("500x700")
        self.root.resizable(False, False)
        
        # Set icon
        try:
            self.root.iconbitmap("logo.ico")
        except:
            pass
        
        # Colors
        self.colors = {
            'bg_primary': '#1e293b',
            'bg_secondary': '#334155', 
            'bg_light': '#f8fafc',
            'accent': '#3b82f6',
            'text_primary': '#ffffff',
            'text_secondary': '#94a3b8',
            'success': '#10b981',
            'warning': '#f59e0b'
        }
        
        self.root.configure(bg=self.colors['bg_primary'])
        self.create_activation_ui()

    def create_activation_ui(self):
        """Create activation interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], padx=30, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Logo
        try:
            logo_image = Image.open("logo.ico")
            logo_image = logo_image.resize((80, 80), Image.Resampling.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_image)
            
            logo_label = tk.Label(main_frame, image=self.logo_photo, bg=self.colors['bg_primary'])
            logo_label.pack(pady=(0, 15))
        except:
            pass
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="🔐 SOFTWARE ACTIVATION",
            font=("Segoe UI", 20, "bold"),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary']
        )
        title_label.pack(pady=(0, 10))
        
        # Subtitle
        subtitle_label = tk.Label(
            main_frame,
            text="Enter your credentials to activate the software",
            font=("Segoe UI", 11),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_primary']
        )
        subtitle_label.pack(pady=(0, 30))
        
        # Activation form
        form_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], padx=30, pady=30)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email field
        tk.Label(form_frame, text="Email Address:", font=("Segoe UI", 12, "bold"),
                fg=self.colors['text_primary'], bg=self.colors['bg_secondary']).pack(anchor=tk.W, pady=(0, 8))
        
        self.email_entry = tk.Entry(form_frame, font=("Segoe UI", 12),
                                   bg=self.colors['bg_light'], fg='#1e293b', relief=tk.FLAT, bd=0,
                                   highlightthickness=2, highlightcolor=self.colors['accent'])
        self.email_entry.pack(fill=tk.X, pady=(0, 20), ipady=10)

        # Password field
        tk.Label(form_frame, text="Password:", font=("Segoe UI", 12, "bold"),
                fg=self.colors['text_primary'], bg=self.colors['bg_secondary']).pack(anchor=tk.W, pady=(0, 8))
        
        self.password_entry = tk.Entry(form_frame, show="*", font=("Segoe UI", 12),
                                      bg=self.colors['bg_light'], fg='#1e293b', relief=tk.FLAT, bd=0,
                                      highlightthickness=2, highlightcolor=self.colors['accent'])
        self.password_entry.pack(fill=tk.X, pady=(0, 25), ipady=10)

        # Activate button
        activate_btn = tk.Button(form_frame, text="🚀 ACTIVATE SOFTWARE", command=self.activate_software,
                               font=("Segoe UI", 14, "bold"), bg=self.colors['accent'], fg=self.colors['text_primary'],
                               relief=tk.FLAT, bd=0, cursor="hand2", activebackground='#2563eb',
                               activeforeground=self.colors['text_primary'])
        activate_btn.pack(fill=tk.X, pady=(0, 15), ipady=12)

        # Hardware ID section
        hw_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], padx=25, pady=25)
        hw_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(hw_frame, text="🖥️ Hardware ID", font=("Segoe UI", 14, "bold"),
                fg=self.colors['text_primary'], bg=self.colors['bg_secondary']).pack(pady=(0, 8))
        
        tk.Label(hw_frame, text="This unique ID identifies your device", font=("Segoe UI", 10),
                fg=self.colors['text_secondary'], bg=self.colors['bg_secondary']).pack(pady=(0, 15))
        
        # Hardware ID display
        hw_id_frame = tk.Frame(hw_frame, bg=self.colors['bg_secondary'])
        hw_id_frame.pack(fill=tk.X)
        
        self.hwid = self.activation.get_hardware_id()
        self.hw_id_var = tk.StringVar(value=self.hwid)
        
        hw_entry = tk.Entry(hw_id_frame, textvariable=self.hw_id_var, state="readonly",
                           font=("Consolas", 11), bg=self.colors['bg_light'], fg='#1e293b',
                           relief=tk.FLAT, bd=0, justify=tk.CENTER)
        hw_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=8)
        
        copy_btn = tk.Button(hw_id_frame, text="📋 COPY", command=self.copy_hardware_id,
                           font=("Segoe UI", 10, "bold"), bg=self.colors['success'], fg=self.colors['text_primary'],
                           relief=tk.FLAT, bd=0, cursor="hand2", width=10)
        copy_btn.pack(side=tk.RIGHT, padx=(15, 0), ipady=8)

        # Contact section
        self.create_contact_section(main_frame)

    def create_contact_section(self, parent):
        """Create contact section"""
        contact_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], padx=25, pady=25)
        contact_frame.pack(fill=tk.X)
        
        tk.Label(contact_frame, text="📞 Need Help? Contact Us", font=("Segoe UI", 13, "bold"),
                fg=self.colors['text_primary'], bg=self.colors['bg_secondary']).pack(pady=(0, 20))
        
        buttons_frame = tk.Frame(contact_frame, bg=self.colors['bg_secondary'])
        buttons_frame.pack()
        
        # Contact buttons
        tk.Button(buttons_frame, text="📱 WhatsApp", command=lambda: webbrowser.open("https://wa.me/201200578402"),
                 font=("Segoe UI", 10, "bold"), bg="#25D366", fg="white", relief=tk.FLAT, bd=0,
                 cursor="hand2", width=14, activebackground="#1DA851").pack(side=tk.LEFT, padx=8, ipady=8)
        
        tk.Button(buttons_frame, text="📘 Facebook", command=lambda: webbrowser.open("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v"),
                 font=("Segoe UI", 10, "bold"), bg="#1877f2", fg="white", relief=tk.FLAT, bd=0,
                 cursor="hand2", width=14, activebackground="#166fe5").pack(side=tk.LEFT, padx=8, ipady=8)
        
        tk.Button(buttons_frame, text="✈️ Telegram", command=lambda: webbrowser.open("http://t.me/Mohamed_Abdo26"),
                 font=("Segoe UI", 10, "bold"), bg="#0088cc", fg="white", relief=tk.FLAT, bd=0,
                 cursor="hand2", width=14, activebackground="#006699").pack(side=tk.LEFT, padx=8, ipady=8)

    def copy_hardware_id(self):
        """Copy hardware ID to clipboard"""
        pyperclip.copy(self.hwid)
        messagebox.showinfo("✅ Copied", "Hardware ID copied to clipboard successfully!")

    def activate_software(self):
        """Activate the software"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not email or not password:
            messagebox.showerror("❌ Error", "Please enter both email and password")
            return
        
        if "@" not in email:
            messagebox.showerror("❌ Error", "Please enter a valid email address")
            return
        
        # Show loading
        self.root.config(cursor="wait")
        self.root.update()
        
        try:
            if self.activation.validate_user(email, password, self.hwid):
                if self.activation.save_license():
                    messagebox.showinfo(
                        "🎉 Success", 
                        f"Software activated successfully!\n\n"
                        f"✅ Email: {email}\n"
                        f"✅ Hardware ID: {self.hwid}\n\n"
                        f"You can now run the main application."
                    )
                    
                    # Ask if user wants to close activation window
                    if messagebox.askyesno("Close Activation", "Activation complete! Close this window?"):
                        self.root.destroy()
                else:
                    messagebox.showerror("❌ Error", "Failed to create license file. Please contact support.")
            else:
                messagebox.showerror(
                    "❌ Activation Failed", 
                    "Invalid credentials or hardware ID not authorized.\n\n"
                    "Please check:\n"
                    "• Email and password are correct\n"
                    "• Your hardware ID is registered\n\n"
                    "Contact support if you need assistance."
                )
        except Exception as e:
            messagebox.showerror("❌ Error", f"Activation failed: {str(e)}")
        finally:
            self.root.config(cursor="")

if __name__ == "__main__":
    root = tk.Tk()
    
    # Center window
    root.update_idletasks()
    width = 500
    height = 700
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    app = ActivationApp(root)
    root.mainloop()
