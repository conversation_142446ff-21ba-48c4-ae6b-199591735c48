#!/usr/bin/env python3
"""
Local Software Activation System - Clean Version
Main application for end users
"""

import tkinter as tk
from tkinter import messagebox
import hashlib
import json
import os
import base64
import uuid

# Configuration
LICENSE_FILE = "license.key"
SECRET_KEY = "MySecretKey2024!@#"  # Shared secret between developer and app

def get_hardware_id():
    """Get unique hardware ID"""
    return str(uuid.getnode())

def create_signature(email, password, hwid):
    """Create SHA256 signature"""
    data = f"{email}{password}{hwid}{SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()

def is_activated():
    """Check if software is already activated"""
    if not os.path.exists(LICENSE_FILE):
        return False
    
    try:
        with open(LICENSE_FILE, "r") as f:
            encoded_data = f.read().strip()
        
        # Decode base64
        decoded_data = base64.b64decode(encoded_data).decode()
        license_data = json.loads(decoded_data)
        
        # Verify license
        stored_hwid = license_data.get("hwid", "")
        stored_signature = license_data.get("signature", "")
        
        # Check hardware ID
        current_hwid = get_hardware_id()
        if stored_hwid != current_hwid:
            return False
        
        # License is valid if HWID matches and signature exists
        return len(stored_signature) == 64  # SHA256 length
        
    except Exception as e:
        print(f"License verification error: {e}")
        return False

class ActivationApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Software Activation")
        self.root.geometry("450x400")
        self.root.resizable(False, False)
        self.root.configure(bg='#f0f0f0')
        
        # Center window
        self.center_window()
        
        # Check if already activated
        if is_activated():
            self.show_main_app()
        else:
            self.show_activation()

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = 450
        height = 400
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def show_activation(self):
        """Show activation interface"""
        self.clear_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=40, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="🔐 Software Activation", 
                              font=("Arial", 18, "bold"), bg='#f0f0f0', fg='#333')
        title_label.pack(pady=(0, 30))
        
        # Email field
        tk.Label(main_frame, text="Email:", font=("Arial", 12, "bold"), 
                bg='#f0f0f0', fg='#333').pack(anchor=tk.W, pady=(0, 5))
        
        self.email_entry = tk.Entry(main_frame, font=("Arial", 11), width=35, 
                                   relief=tk.SOLID, bd=1)
        self.email_entry.pack(pady=(0, 15), ipady=5)
        
        # Password field
        tk.Label(main_frame, text="Password:", font=("Arial", 12, "bold"), 
                bg='#f0f0f0', fg='#333').pack(anchor=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(main_frame, show="*", font=("Arial", 11), 
                                      width=35, relief=tk.SOLID, bd=1)
        self.password_entry.pack(pady=(0, 20), ipady=5)
        
        # Activate button
        activate_btn = tk.Button(main_frame, text="🚀 Activate Software", 
                               command=self.activate_software,
                               font=("Arial", 12, "bold"), bg='#4CAF50', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=25, pady=8)
        activate_btn.pack(pady=(0, 20))
        
        # Hardware ID section
        hw_frame = tk.Frame(main_frame, bg='#e8e8e8', relief=tk.SOLID, bd=1)
        hw_frame.pack(fill=tk.X, pady=(10, 0), padx=10, ipady=15)
        
        tk.Label(hw_frame, text="Hardware ID:", font=("Arial", 10, "bold"), 
                bg='#e8e8e8', fg='#666').pack()
        
        hwid = get_hardware_id()
        tk.Label(hw_frame, text=hwid, font=("Courier", 9), 
                bg='#e8e8e8', fg='#333').pack(pady=(5, 0))
        
        tk.Label(hw_frame, text="(Send this ID to get activation)", 
                font=("Arial", 8), bg='#e8e8e8', fg='#888').pack()

    def show_main_app(self):
        """Show main application after activation"""
        self.clear_window()
        self.root.title("My Software - Licensed")
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=40, pady=40)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Success message
        tk.Label(main_frame, text="✅ Software Activated Successfully!", 
                font=("Arial", 16, "bold"), bg='#f0f0f0', fg='#4CAF50').pack(pady=(0, 30))
        
        # Welcome message
        tk.Label(main_frame, text="Welcome to your licensed software!\nAll features are now available.", 
                font=("Arial", 12), bg='#f0f0f0', fg='#333', justify=tk.CENTER).pack(pady=(0, 30))
        
        # Sample features
        features_frame = tk.Frame(main_frame, bg='#f0f0f0')
        features_frame.pack(pady=20)
        
        tk.Button(features_frame, text="📊 Feature 1", command=self.feature1,
                 font=("Arial", 10), bg='#2196F3', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=15, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(features_frame, text="⚙️ Feature 2", command=self.feature2,
                 font=("Arial", 10), bg='#FF9800', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=15, pady=5).pack(side=tk.LEFT, padx=5)
        
        # License info
        try:
            with open(LICENSE_FILE, "r") as f:
                encoded_data = f.read().strip()
            decoded_data = base64.b64decode(encoded_data).decode()
            license_data = json.loads(decoded_data)
            
            info_text = f"Licensed to: {license_data.get('email', 'Unknown')}\nHardware ID: {license_data.get('hwid', 'Unknown')}"
            tk.Label(main_frame, text=info_text, font=("Arial", 9), 
                    bg='#f0f0f0', fg='#666', justify=tk.CENTER).pack(pady=(30, 0))
        except:
            pass
        
        # Exit button
        tk.Button(main_frame, text="Exit", command=self.root.quit,
                 font=("Arial", 10), bg='#f44336', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=10, pady=5).pack(pady=(20, 0))

    def activate_software(self):
        """Handle software activation - This is just a demo"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not email or not password:
            messagebox.showerror("Error", "Please enter both email and password")
            return
        
        if "@" not in email:
            messagebox.showerror("Error", "Please enter a valid email address")
            return
        
        # Demo activation - In real scenario, you would verify against your database
        messagebox.showinfo("Demo", "This is a demo version.\nUse the license generator to create a real license file.")

    def feature1(self):
        """Sample feature 1"""
        messagebox.showinfo("Feature 1", "This is Feature 1 of your licensed software!")

    def feature2(self):
        """Sample feature 2"""
        messagebox.showinfo("Feature 2", "This is Feature 2 of your licensed software!")

    def clear_window(self):
        """Clear all widgets from window"""
        for widget in self.root.winfo_children():
            widget.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = ActivationApp(root)
    root.mainloop()
