# Requirements for End User Application
# No external dependencies required - uses only Python standard library

# Python Standard Library modules used:
# - tkinter (GUI framework)
# - hashlib (cryptographic hashing)
# - json (JSON data handling)
# - os (operating system interface)
# - base64 (base64 encoding/decoding)
# - uuid (UUID generation)
# - platform (platform identification)
# - subprocess (subprocess management)

# Minimum Python version: 3.6+
