#!/usr/bin/env python3
"""
Test activation system functionality
"""

import sys
import os
sys.path.append('.')

from main import ActivationSystem
import json

def test_hardware_id():
    """Test hardware ID generation"""
    print("🔧 Testing Hardware ID Generation...")
    
    activation = ActivationSystem()
    hwid = activation.get_hardware_id()
    
    print(f"✅ Hardware ID: {hwid}")
    print(f"✅ Format: {len(hwid)} characters")
    
    # Test consistency
    hwid2 = activation.get_hardware_id()
    if hwid == hwid2:
        print("✅ Hardware ID is consistent")
        return hwid
    else:
        print("❌ Hardware ID inconsistent!")
        return None

def test_user_loading():
    """Test loading users from encrypted file"""
    print("\n🔐 Testing User Loading...")
    
    activation = ActivationSystem()
    users = activation.load_users()
    
    if users:
        print(f"✅ Loaded {len(users)} users")
        for i, user in enumerate(users, 1):
            print(f"   {i}. {user['email']} | HWID: {user['hwid']}")
        return users
    else:
        print("❌ Failed to load users")
        return []

def test_validation():
    """Test user validation"""
    print("\n✅ Testing User Validation...")
    
    activation = ActivationSystem()
    
    # Test with valid credentials
    test_cases = [
        ("<EMAIL>", "123456", "ABCD-1234-EFGH-5678"),
        ("<EMAIL>", "admin123", "WXYZ-9876-MNOP-5432"),
        ("<EMAIL>", "demo2024", "QRST-1111-UVWX-2222"),
        ("<EMAIL>", "wrong", "WRONG-HWID-HERE"),
    ]
    
    for email, password, hwid in test_cases:
        result = activation.validate_user(email, password, hwid)
        status = "✅" if result else "❌"
        print(f"   {status} {email} | {password} | {hwid}")

def test_license_creation():
    """Test license creation"""
    print("\n📄 Testing License Creation...")
    
    activation = ActivationSystem()
    
    # Remove existing license
    if os.path.exists(activation.license_file):
        os.remove(activation.license_file)
        print("   Removed existing license")
    
    # Create new license
    if activation.create_license():
        print("✅ License created successfully")
        
        # Check license content
        with open(activation.license_file, 'r') as f:
            license_data = json.load(f)
        
        print(f"   Hardware ID: {license_data['hardware_id']}")
        print(f"   Activated: {license_data['activated_date']}")
        print(f"   Status: {license_data['status']}")
        
        return True
    else:
        print("❌ Failed to create license")
        return False

def test_license_check():
    """Test license checking"""
    print("\n🔍 Testing License Check...")
    
    activation = ActivationSystem()
    
    if activation.check_license():
        print("✅ License check passed")
        return True
    else:
        print("❌ License check failed")
        return False

def main():
    """Run all tests"""
    print("🧪 ACTIVATION SYSTEM FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test hardware ID
    hwid = test_hardware_id()
    if not hwid:
        print("❌ Hardware ID test failed - stopping")
        return
    
    # Test user loading
    users = test_user_loading()
    if not users:
        print("❌ User loading failed - stopping")
        return
    
    # Test validation
    test_validation()
    
    # Test license creation
    if test_license_creation():
        # Test license checking
        test_license_check()
    
    print("\n" + "=" * 50)
    print("🎯 FULL ACTIVATION TEST")
    print("Testing with valid credentials...")
    
    activation = ActivationSystem()
    current_hwid = activation.get_hardware_id()
    
    # Try to find a matching user for current hardware
    test_user = None
    for user in users:
        if user['hwid'] == current_hwid:
            test_user = user
            break
    
    if test_user:
        print(f"✅ Found matching user for current hardware: {test_user['email']}")
        
        # Test full activation flow
        if activation.validate_user(test_user['email'], test_user['password'], current_hwid):
            print("✅ Validation successful")
            
            if activation.create_license():
                print("✅ License creation successful")
                
                if activation.check_license():
                    print("✅ License verification successful")
                    print("\n🎉 FULL ACTIVATION FLOW WORKS!")
                else:
                    print("❌ License verification failed")
            else:
                print("❌ License creation failed")
        else:
            print("❌ Validation failed")
    else:
        print(f"ℹ️  No user found for current hardware ID: {current_hwid}")
        print("   This is normal - add your hardware ID to users.json to test full flow")

if __name__ == "__main__":
    main()
