# 🔐 Software Activation System

نظام تفعيل برمجيات احترافي يعمل بدون سيرفر مع حماية قوية

## 📁 ملفات المشروع

### للمطور (Developer Files):
- `main.py` - الكود المصدري للبرنامج الرئيسي
- `encrypt_users.py` - أداة تشفير بيانات المستخدمين
- `users.json` - ملف بيانات المستخدمين (غير مشفر)
- `secret.key` - مفتاح التشفير (احتفظ به سرياً)
- `create_encrypted_files.py` - سكريبت سريع لإنشاء الملفات المشفرة

### للتوزيع (Distribution Files):
- `main.exe` - البرنامج النهائي
- `users.enc` - ملف المستخدمين المشفر
- `logo.ico` - أيقونة البرنامج

## 🚀 كيفية الاستخدام

### 1. إعداد بيانات المستخدمين:
```json
[
  {
    "email": "<EMAIL>",
    "password": "123456", 
    "hwid": "ABCD-1234-EFGH-5678"
  }
]
```

### 2. تشفير البيانات:
```bash
python create_encrypted_files.py
```

### 3. إنشاء ملف EXE:
```bash
pyinstaller --onefile --windowed --icon=logo.ico --add-data "users.enc;." main.py
```

### 4. التوزيع:
- أرسل للعميل: `main.exe` + `users.enc`
- احتفظ بـ: `secret.key` (لا ترسله أبداً)

## 🔧 بيانات الاختبار

### المستخدمين المتاحين:
1. **Email:** <EMAIL>  
   **Password:** 123456  
   **HWID:** ABCD-1234-EFGH-5678

2. **Email:** <EMAIL>  
   **Password:** admin123  
   **HWID:** WXYZ-9876-MNOP-5432

3. **Email:** <EMAIL>  
   **Password:** demo2024  
   **HWID:** QRST-1111-UVWX-2222

## 🛡️ الحماية

- ✅ تشفير AES-256 للبيانات
- ✅ Hardware ID فريد لكل جهاز
- ✅ ملف ترخيص محلي (license.key)
- ✅ لا يعمل إلا على الجهاز المفعل
- ✅ بدون اتصال بالإنترنت

## 📋 خطوات التفعيل

1. العميل يشغل `main.exe`
2. يدخل Email + Password
3. البرنامج يقرأ Hardware ID تلقائياً
4. يتم التحقق من البيانات المشفرة
5. إنشاء `license.key` للجهاز
6. البرنامج يعمل بشكل دائم

## 🔄 إضافة مستخدمين جدد

1. عدّل `users.json`
2. شغّل `python create_encrypted_files.py`
3. أرسل `users.enc` الجديد للعميل

## 📞 معلومات التواصل

- 📱 WhatsApp: https://wa.me/201200578402
- ✈️ Telegram: http://t.me/Mohamed_Abdo26
- 📘 Facebook: https://www.facebook.com/mohamed.abdalkareem.558739

## ⚠️ ملاحظات مهمة

- لا تشارك `secret.key` مع أي شخص
- احتفظ بنسخة احتياطية من `users.json`
- كل Hardware ID يعمل على جهاز واحد فقط
- البرنامج محمي ضد النسخ والتوزيع غير المشروع
