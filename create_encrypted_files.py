#!/usr/bin/env python3
"""
Simple script to create encrypted files
"""

from cryptography.fernet import Fernet
import json

# Generate key
key = Fernet.generate_key()
print(f"Generated key: {key.decode()}")

# Save key
with open('secret.key', 'wb') as f:
    f.write(key)
print("✅ secret.key created")

# Encrypt users.json
fernet = Fernet(key)
with open('users.json', 'r') as f:
    data = f.read()

encrypted = fernet.encrypt(data.encode())
with open('users.enc', 'wb') as f:
    f.write(encrypted)
print("✅ users.enc created")

print("\n🎉 All files created successfully!")
print("Files to distribute: main.exe + users.enc")
print("Keep secret: secret.key")
