import tkinter as tk
from tkinter import ttk, messagebox
import uuid
import platform
import subprocess
import hashlib
import secrets
import pyperclip  # للنسخ إلى الحافظة

class LoginApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تسجيل الدخول")
        self.root.geometry("400x350")
        self.root.resizable(False, False)
        
        # الحصول على هاردوير آيدي الجهاز
        self.hardware_id = self.get_hardware_id()
        
        # إنشاء الإطار الرئيسي
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان التطبيق
        title_label = ttk.Label(main_frame, text="تسجيل الدخول", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # إطار هاردوير آيدي
        hw_frame = ttk.Frame(main_frame)
        hw_frame.pack(fill=tk.X, pady=10)
        
        hw_label = ttk.Label(hw_frame, text="هاردوير آيدي:", font=("Arial", 10))
        hw_label.pack(side=tk.LEFT, padx=5)
        
        self.hw_id_var = tk.StringVar(value=self.hardware_id)
        hw_entry = ttk.Entry(hw_frame, textvariable=self.hw_id_var, state="readonly", width=30)
        hw_entry.pack(side=tk.LEFT, padx=5)
        
        copy_button = ttk.Button(hw_frame, text="نسخ", width=5, command=self.copy_hardware_id)
        copy_button.pack(side=tk.LEFT, padx=5)
        
        # إطار معلومات تسجيل الدخول
        login_frame = ttk.Frame(main_frame)
        login_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # حقل البريد الإلكتروني
        email_label = ttk.Label(login_frame, text="البريد الإلكتروني:", font=("Arial", 10))
        email_label.pack(anchor=tk.W, pady=(10, 5))
        
        self.email_var = tk.StringVar()
        email_entry = ttk.Entry(login_frame, textvariable=self.email_var, width=40)
        email_entry.pack(fill=tk.X, pady=(0, 10))
        
        # حقل كلمة المرور
        password_label = ttk.Label(login_frame, text="كلمة المرور:", font=("Arial", 10))
        password_label.pack(anchor=tk.W, pady=(10, 5))
        
        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(login_frame, textvariable=self.password_var, show="*", width=40)
        password_entry.pack(fill=tk.X, pady=(0, 20))
        
        # زر تسجيل الدخول
        login_button = ttk.Button(login_frame, text="تسجيل الدخول", command=self.login)
        login_button.pack(pady=10)
        
    def get_hardware_id(self):
        """الحصول على معرف فريد للجهاز بناءً على معلومات الأجهزة"""
        # جمع معلومات الجهاز
        system_info = platform.system() + platform.version() + platform.machine()
        
        try:
            if platform.system() == "Windows":
                # الحصول على الرقم التسلسلي للقرص الصلب على ويندوز
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            elif platform.system() == "Linux":
                # محاولة الحصول على معرف الجهاز على لينكس
                with open('/etc/machine-id', 'r') as f:
                    system_info += f.read().strip()
            elif platform.system() == "Darwin":  # macOS
                # محاولة الحصول على الرقم التسلسلي على ماك
                result = subprocess.check_output('ioreg -rd1 -c IOPlatformExpertDevice', shell=True).decode()
                for line in result.split('\n'):
                    if 'IOPlatformUUID' in line:
                        system_info += line.split('"')[3]
                        break
        except:
            # إذا فشلت الطرق السابقة، استخدم معرف عشوائي ثابت
            system_info += secrets.token_hex(8)
        
        # إنشاء هاش من معلومات النظام
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        # تنسيق الهاش ليكون أكثر قابلية للقراءة
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        
        return formatted_id.upper()
    
    def copy_hardware_id(self):
        """نسخ هاردوير آيدي إلى الحافظة"""
        pyperclip.copy(self.hardware_id)
        messagebox.showinfo("تم النسخ", "تم نسخ هاردوير آيدي إلى الحافظة")
    
    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        email = self.email_var.get()
        password = self.password_var.get()
        
        if not email or not password:
            messagebox.showerror("خطأ", "يرجى إدخال البريد الإلكتروني وكلمة المرور")
            return
        
        # هنا يمكنك إضافة منطق التحقق من صحة بيانات تسجيل الدخول
        # مثال بسيط للتوضيح:
        if "@" in email and len(password) >= 6:
            messagebox.showinfo("نجاح", f"تم تسجيل الدخول بنجاح\nهاردوير آيدي: {self.hardware_id}")
        else:
            messagebox.showerror("خطأ", "بيانات تسجيل الدخول غير صحيحة")

if __name__ == "__main__":
    root = tk.Tk()
    app = LoginApp(root)
    # تعيين الاتجاه من اليمين إلى اليسار للدعم العربي
    root.tk.call('encoding', 'system', 'utf-8')
    try:
        root.tk.call('tk', 'scaling', 1.5)  # تحسين المظهر على شاشات عالية الدقة
    except:
        pass
    root.mainloop()
