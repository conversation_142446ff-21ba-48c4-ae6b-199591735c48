import tkinter as tk
from tkinter import ttk, messagebox
import uuid
import platform
import subprocess
import hashlib
import secrets
import pyperclip
import webbrowser

class LoginApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Login System")
        self.root.geometry("450x550")
        self.root.resizable(False, False)

        # Professional color scheme
        self.colors = {
            'bg_primary': '#1e293b',      # Dark blue-gray
            'bg_secondary': '#334155',    # Medium blue-gray
            'bg_light': '#f8fafc',        # Light gray
            'accent': '#3b82f6',          # Blue
            'accent_hover': '#2563eb',    # Darker blue
            'text_primary': '#ffffff',    # White
            'text_secondary': '#94a3b8',  # Light gray
            'success': '#10b981',         # Green
            'danger': '#ef4444'           # Red
        }

        # Configure root background
        self.root.configure(bg=self.colors['bg_primary'])

        # Get hardware ID
        self.hardware_id = self.get_hardware_id()

        # Create main container
        self.create_ui()

    def create_ui(self):
        """Create the user interface"""
        # Main container with custom styling
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], padx=30, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = tk.Label(
            main_frame,
            text="LOGIN SYSTEM",
            font=("Segoe UI", 20, "bold"),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary']
        )
        title_label.pack(pady=(0, 30))

        # Login form container
        form_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], padx=25, pady=25)
        form_frame.pack(fill=tk.X, pady=(0, 20))

        # Email field
        email_label = tk.Label(
            form_frame,
            text="Email:",
            font=("Segoe UI", 11, "bold"),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        email_label.pack(anchor=tk.W, pady=(0, 5))

        self.email_var = tk.StringVar()
        email_entry = tk.Entry(
            form_frame,
            textvariable=self.email_var,
            font=("Segoe UI", 11),
            bg=self.colors['bg_light'],
            fg='#1e293b',
            relief=tk.FLAT,
            bd=0,
            highlightthickness=2,
            highlightcolor=self.colors['accent']
        )
        email_entry.pack(fill=tk.X, pady=(0, 15), ipady=8)

        # Password field
        password_label = tk.Label(
            form_frame,
            text="Password:",
            font=("Segoe UI", 11, "bold"),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        password_label.pack(anchor=tk.W, pady=(0, 5))

        self.password_var = tk.StringVar()
        password_entry = tk.Entry(
            form_frame,
            textvariable=self.password_var,
            show="*",
            font=("Segoe UI", 11),
            bg=self.colors['bg_light'],
            fg='#1e293b',
            relief=tk.FLAT,
            bd=0,
            highlightthickness=2,
            highlightcolor=self.colors['accent']
        )
        password_entry.pack(fill=tk.X, pady=(0, 20), ipady=8)

        # Login button
        login_button = tk.Button(
            form_frame,
            text="LOGIN",
            command=self.login,
            font=("Segoe UI", 12, "bold"),
            bg=self.colors['accent'],
            fg=self.colors['text_primary'],
            relief=tk.FLAT,
            bd=0,
            cursor="hand2",
            activebackground=self.colors['accent_hover'],
            activeforeground=self.colors['text_primary']
        )
        login_button.pack(fill=tk.X, pady=(0, 10), ipady=10)

        # Hardware ID section
        hw_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], padx=25, pady=20)
        hw_frame.pack(fill=tk.X, pady=(0, 20))

        hw_title = tk.Label(
            hw_frame,
            text="Hardware ID",
            font=("Segoe UI", 12, "bold"),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        hw_title.pack(pady=(0, 5))

        hw_subtitle = tk.Label(
            hw_frame,
            text="To activate the program",
            font=("Segoe UI", 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary']
        )
        hw_subtitle.pack(pady=(0, 10))

        # Hardware ID display and copy
        hw_id_frame = tk.Frame(hw_frame, bg=self.colors['bg_secondary'])
        hw_id_frame.pack(fill=tk.X)

        self.hw_id_var = tk.StringVar(value=self.hardware_id)
        hw_entry = tk.Entry(
            hw_id_frame,
            textvariable=self.hw_id_var,
            state="readonly",
            font=("Consolas", 10),
            bg=self.colors['bg_light'],
            fg='#1e293b',
            relief=tk.FLAT,
            bd=0,
            justify=tk.CENTER
        )
        hw_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5)

        copy_button = tk.Button(
            hw_id_frame,
            text="COPY",
            command=self.copy_hardware_id,
            font=("Segoe UI", 9, "bold"),
            bg=self.colors['success'],
            fg=self.colors['text_primary'],
            relief=tk.FLAT,
            bd=0,
            cursor="hand2",
            width=8
        )
        copy_button.pack(side=tk.RIGHT, padx=(10, 0), ipady=5)

        # Social media section
        self.create_social_media_section(main_frame)

    def create_social_media_section(self, parent):
        """Create social media contact section"""
        social_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], padx=25, pady=15)
        social_frame.pack(fill=tk.X)

        contact_label = tk.Label(
            social_frame,
            text="Contact Us",
            font=("Segoe UI", 12, "bold"),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        contact_label.pack(pady=(0, 15))

        # Social media buttons container
        buttons_frame = tk.Frame(social_frame, bg=self.colors['bg_secondary'])
        buttons_frame.pack()

        # WhatsApp button
        whatsapp_btn = tk.Button(
            buttons_frame,
            text="📱 WhatsApp",
            command=self.open_whatsapp,
            font=("Segoe UI", 10, "bold"),
            bg="#25D366",
            fg="white",
            relief=tk.FLAT,
            bd=0,
            cursor="hand2",
            width=12,
            activebackground="#1DA851",
            activeforeground="white"
        )
        whatsapp_btn.pack(side=tk.LEFT, padx=5, ipady=8)

        # Telegram button
        telegram_btn = tk.Button(
            buttons_frame,
            text="✈️ Telegram",
            command=self.open_telegram,
            font=("Segoe UI", 10, "bold"),
            bg="#0088cc",
            fg="white",
            relief=tk.FLAT,
            bd=0,
            cursor="hand2",
            width=12,
            activebackground="#006699",
            activeforeground="white"
        )
        telegram_btn.pack(side=tk.LEFT, padx=5, ipady=8)

        # Facebook button
        facebook_btn = tk.Button(
            buttons_frame,
            text="📘 Facebook",
            command=self.open_facebook,
            font=("Segoe UI", 10, "bold"),
            bg="#1877f2",
            fg="white",
            relief=tk.FLAT,
            bd=0,
            cursor="hand2",
            width=12,
            activebackground="#166fe5",
            activeforeground="white"
        )
        facebook_btn.pack(side=tk.LEFT, padx=5, ipady=8)

    def open_whatsapp(self):
        """Open WhatsApp contact"""
        webbrowser.open("https://wa.me/201200578402")

    def open_telegram(self):
        """Open Telegram contact"""
        webbrowser.open("http://t.me/Mohamed_Abdo26")

    def open_facebook(self):
        """Open Facebook contact"""
        webbrowser.open("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v")

    def get_hardware_id(self):
        """الحصول على معرف فريد للجهاز بناءً على معلومات الأجهزة"""
        # جمع معلومات الجهاز
        system_info = platform.system() + platform.version() + platform.machine()
        
        try:
            if platform.system() == "Windows":
                # الحصول على الرقم التسلسلي للقرص الصلب على ويندوز
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            elif platform.system() == "Linux":
                # محاولة الحصول على معرف الجهاز على لينكس
                with open('/etc/machine-id', 'r') as f:
                    system_info += f.read().strip()
            elif platform.system() == "Darwin":  # macOS
                # محاولة الحصول على الرقم التسلسلي على ماك
                result = subprocess.check_output('ioreg -rd1 -c IOPlatformExpertDevice', shell=True).decode()
                for line in result.split('\n'):
                    if 'IOPlatformUUID' in line:
                        system_info += line.split('"')[3]
                        break
        except:
            # إذا فشلت الطرق السابقة، استخدم معرف عشوائي ثابت
            system_info += secrets.token_hex(8)
        
        # إنشاء هاش من معلومات النظام
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        # تنسيق الهاش ليكون أكثر قابلية للقراءة
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        
        return formatted_id.upper()
    
    def copy_hardware_id(self):
        """Copy hardware ID to clipboard"""
        pyperclip.copy(self.hardware_id)
        messagebox.showinfo("Copied", "Hardware ID copied to clipboard successfully!")
    
    def login(self):
        """Validate login credentials"""
        email = self.email_var.get().strip()
        password = self.password_var.get().strip()

        if not email or not password:
            messagebox.showerror("Error", "Please enter both email and password")
            return

        # Basic validation (you can add more sophisticated validation here)
        if "@" in email and len(password) >= 6:
            messagebox.showinfo(
                "Success",
                f"Login successful!\n\nHardware ID: {self.hardware_id}\n\nContact us for activation."
            )
        else:
            messagebox.showerror("Error", "Invalid email or password (minimum 6 characters)")

if __name__ == "__main__":
    root = tk.Tk()

    # Center the window on screen
    root.update_idletasks()
    width = 450
    height = 550
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    # Create the application
    app = LoginApp(root)

    # Set encoding and scaling for better display
    root.tk.call('encoding', 'system', 'utf-8')
    try:
        root.tk.call('tk', 'scaling', 1.0)  # Adjust scaling for better appearance
    except:
        pass

    root.mainloop()
