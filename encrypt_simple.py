import json
import os
from cryptography.fernet import Fernet
import uuid
import hashlib
import platform
import subprocess

def get_hardware_id():
    """Get unique hardware ID"""
    try:
        # Get system info
        system_info = platform.system() + platform.version() + platform.machine()
        
        # Get UUID from system
        if platform.system() == "Windows":
            result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
            uuid_result = result.split('\n')[1].strip()
            system_info += uuid_result
        else:
            system_info += str(uuid.getnode())
        
        # Create hash
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        return formatted_id.upper()
    except:
        # Fallback
        node = str(uuid.getnode())
        return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()

# مفتاح التشفير الثابت (نفس المفتاح في البرنامج الرئيسي)
SECRET_KEY = b'4ibKLILdcFR7E--NU8rH2eFBf90nOSDwwjnHBk2RO00='

fernet = Fernet(SECRET_KEY)

# بيانات المستخدمين
users = [
    {
        "email": "<EMAIL>",
        "password": "123456",
        "hwid": "ABCD-1234-EFGH-5678"
    },
    {
        "email": "<EMAIL>", 
        "password": "admin123",
        "hwid": "WXYZ-9876-MNOP-5432"
    },
    {
        "email": "<EMAIL>",
        "password": "demo2024", 
        "hwid": "QRST-1111-UVWX-2222"
    },
    {
        "email": "<EMAIL>",
        "password": "dev123",
        "hwid": get_hardware_id()  # Hardware ID الحالي للاختبار
    }
]

# حفظ النسخة العادية (للمطور فقط)
with open("users_plain.json", "w", encoding='utf-8') as f:
    json.dump(users, f, indent=4, ensure_ascii=False)

# تشفير البيانات
encrypted_data = fernet.encrypt(json.dumps(users, ensure_ascii=False).encode('utf-8'))

# حفظ النسخة المشفرة
with open("users.enc", "wb") as f:
    f.write(encrypted_data)

print("✅ تم حفظ المستخدمين وتشفيرهم.")
print(f"📁 ملفات تم إنشاؤها:")
print(f"   - users_plain.json (للمطور فقط)")
print(f"   - users.enc (للتوزيع)")
print(f"\n🔧 بيانات الاختبار:")
for i, user in enumerate(users, 1):
    print(f"   {i}. {user['email']} | {user['password']} | {user['hwid']}")
print(f"\n💻 Hardware ID الحالي: {get_hardware_id()}")
