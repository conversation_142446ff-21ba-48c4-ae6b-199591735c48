# 🔐 دليل المستخدم النهائي - نظام التفعيل المحلي

## 📋 **نظرة عامة:**
هذا البرنامج يتطلب تفعيل محلي لضمان الاستخدام المرخص. النظام يعمل بدون الحاجة لاتصال بالإنترنت بعد التفعيل.

## 🚀 **التشغيل الأول:**

### **الخطوة 1: تشغيل البرنامج**
```bash
python main.py
```
أو شغل الملف التنفيذي `MySoftware.exe`

### **الخطوة 2: شاشة التفعيل**
عند التشغيل الأول، ستظهر شاشة التفعيل تطلب:
- 📧 **البريد الإلكتروني**
- 🔑 **كلمة المرور**
- 💻 **معرف الجهاز** (يظهر تلقائياً)

### **الخطوة 3: طلب التفعيل**
1. أدخل بريدك الإلكتروني وكلمة المرور
2. انسخ معرف الجهاز (Hardware ID) باستخدام زر "Copy"
3. اضغط "Request Activation"
4. أرسل البيانات الظاهرة لمقدم البرنامج

### **الخطوة 4: استلام الترخيص**
- ستستلم ملف `license.key` من مقدم البرنامج
- ضع الملف في نفس مجلد البرنامج
- أعد تشغيل البرنامج

## ✅ **بعد التفعيل:**
- البرنامج سيعمل مباشرة بدون طلب تفعيل مرة أخرى
- جميع الميزات ستكون متاحة
- لا حاجة لاتصال بالإنترنت

## 📁 **هيكل الملفات:**
```
📁 MyProgram/
├── main.py              # البرنامج الرئيسي
├── license.key          # ملف الترخيص (بعد التفعيل)
└── README.md            # هذا الملف
```

## ⚠️ **ملاحظات مهمة:**

### **الأمان:**
- ملف `license.key` مرتبط بجهازك فقط
- لا تشارك ملف الترخيص مع أجهزة أخرى
- احتفظ بنسخة احتياطية من ملف الترخيص

### **استكشاف الأخطاء:**
- **"License file not found"**: ضع ملف `license.key` مع البرنامج
- **"Hardware ID mismatch"**: الترخيص لا يعمل على هذا الجهاز
- **"Invalid signature"**: ملف الترخيص تالف أو معدل

## 🔧 **المتطلبات:**
- Python 3.6 أو أحدث
- نظام التشغيل: Windows, macOS, Linux
- لا يتطلب مكتبات إضافية (يستخدم مكتبات Python الأساسية)

## 📞 **الدعم الفني:**
إذا واجهت أي مشاكل:
1. تأكد من وجود ملف `license.key` في نفس مجلد البرنامج
2. تأكد من أن ملف الترخيص صحيح وغير معدل
3. تواصل مع مقدم البرنامج مع معرف الجهاز الخاص بك

## 🎯 **الميزات المتاحة بعد التفعيل:**
- 📊 **Analytics**: عرض وتحليل البيانات
- ⚙️ **Settings**: إعدادات البرنامج
- 📈 **Reports**: إنشاء وتصدير التقارير
- وجميع الميزات الأخرى حسب البرنامج

---
**شكراً لاستخدام برنامجنا! 🙏**
