#!/usr/bin/env python3
"""
Main Application - Separate from activation
Checks for license and runs main program
"""

import tkinter as tk
from tkinter import messagebox
import json
import os
import hashlib
import platform
import subprocess
import uuid
import webbrowser
from PIL import Image, ImageTk
from datetime import datetime

# Configuration
LICENSE_FILE = "license.key"

class LicenseChecker:
    def __init__(self):
        pass
        
    def get_hardware_id(self):
        """Get unique hardware ID"""
        try:
            system_info = platform.system() + platform.version() + platform.machine()
            
            if platform.system() == "Windows":
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            else:
                system_info += str(uuid.getnode())
            
            hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
            formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
            return formatted_id.upper()
        except:
            node = str(uuid.getnode())
            return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()
    
    def check_license(self):
        """Check if valid license exists"""
        try:
            if not os.path.exists(LICENSE_FILE):
                return False, "No license file found"
            
            with open(LICENSE_FILE, 'r') as f:
                license_data = json.load(f)
            
            current_hwid = self.get_hardware_id()
            license_hwid = license_data.get('hardware_id', '')
            
            if license_hwid == current_hwid:
                return True, license_data
            else:
                return False, "Hardware ID mismatch"
                
        except Exception as e:
            return False, f"License check failed: {e}"

class MainApplication:
    def __init__(self, root):
        self.root = root
        self.license_checker = LicenseChecker()
        
        # Check license first
        is_valid, license_info = self.license_checker.check_license()
        
        if is_valid:
            self.show_main_application(license_info)
        else:
            self.show_activation_required(license_info)

    def show_activation_required(self, error_msg):
        """Show activation required message"""
        self.root.title("Activation Required")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        self.root.configure(bg='#1e293b')
        
        # Set icon
        try:
            self.root.iconbitmap("logo.ico")
        except:
            pass
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#1e293b', padx=40, pady=40)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Logo
        try:
            logo_image = Image.open("logo.ico")
            logo_image = logo_image.resize((80, 80), Image.Resampling.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_image)
            
            logo_label = tk.Label(main_frame, image=self.logo_photo, bg='#1e293b')
            logo_label.pack(pady=(0, 20))
        except:
            pass
        
        # Title
        tk.Label(main_frame, text="🔒 ACTIVATION REQUIRED", font=("Segoe UI", 18, "bold"),
                fg='#ef4444', bg='#1e293b').pack(pady=(0, 20))
        
        # Message
        tk.Label(main_frame, text="This software requires activation to run.\nPlease activate your copy first.",
                font=("Segoe UI", 12), fg='#94a3b8', bg='#1e293b', justify=tk.CENTER).pack(pady=(0, 30))
        
        # Hardware ID
        hwid = self.license_checker.get_hardware_id()
        tk.Label(main_frame, text=f"Your Hardware ID: {hwid}", font=("Consolas", 10),
                fg='#64748b', bg='#1e293b').pack(pady=(0, 30))
        
        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg='#1e293b')
        buttons_frame.pack(pady=20)
        
        # Activate button
        tk.Button(buttons_frame, text="🔐 ACTIVATE NOW", command=self.run_activation,
                 font=("Segoe UI", 12, "bold"), bg='#3b82f6', fg='white', relief=tk.FLAT, bd=0,
                 cursor="hand2", width=20, activebackground='#2563eb').pack(pady=10)
        
        # Exit button
        tk.Button(buttons_frame, text="❌ EXIT", command=self.root.quit,
                 font=("Segoe UI", 10), bg='#6b7280', fg='white', relief=tk.FLAT, bd=0,
                 cursor="hand2", width=20, activebackground='#4b5563').pack(pady=5)

    def run_activation(self):
        """Run activation program"""
        try:
            if os.path.exists("activate.py"):
                import subprocess
                subprocess.Popen(["python", "activate.py"])
                messagebox.showinfo("Activation", "Opening activation window...\nThis window will close.")
                self.root.quit()
            elif os.path.exists("activate.exe"):
                import subprocess
                subprocess.Popen(["activate.exe"])
                messagebox.showinfo("Activation", "Opening activation window...\nThis window will close.")
                self.root.quit()
            else:
                messagebox.showerror("Error", "Activation program not found!\nPlease contact support.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to run activation: {e}")

    def show_main_application(self, license_info):
        """Show the main application"""
        self.root.title("My Software - Licensed")
        self.root.geometry("800x600")
        self.root.configure(bg='#f8fafc')
        
        # Set icon
        try:
            self.root.iconbitmap("logo.ico")
        except:
            pass
        
        # Create main app interface
        self.create_main_app_ui(license_info)
    
    def create_main_app_ui(self, license_info):
        """Create the main application interface"""
        # Header frame
        header_frame = tk.Frame(self.root, bg='#1e293b', height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Logo and title in header
        try:
            logo_image = Image.open("logo.ico")
            logo_image = logo_image.resize((50, 50), Image.Resampling.LANCZOS)
            self.header_logo = ImageTk.PhotoImage(logo_image)
            
            logo_label = tk.Label(header_frame, image=self.header_logo, bg='#1e293b')
            logo_label.pack(side=tk.LEFT, padx=20, pady=15)
        except:
            pass
        
        title_label = tk.Label(header_frame, text="MY SOFTWARE", font=("Segoe UI", 18, "bold"),
                              fg='white', bg='#1e293b')
        title_label.pack(side=tk.LEFT, pady=20)
        
        # License status in header
        license_status = tk.Label(header_frame, text="✅ LICENSED", font=("Segoe UI", 10, "bold"),
                                 fg='#10b981', bg='#1e293b')
        license_status.pack(side=tk.RIGHT, padx=20, pady=20)
        
        # Main content area
        content_frame = tk.Frame(self.root, bg='#f8fafc', padx=40, pady=40)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Welcome message
        welcome_label = tk.Label(content_frame, text="🎉 Welcome to Your Licensed Software!",
                                font=("Segoe UI", 24, "bold"), fg='#1e293b', bg='#f8fafc')
        welcome_label.pack(pady=(0, 30))
        
        # License info
        activated_date = license_info.get('activated_date', 'Unknown')
        if activated_date != 'Unknown':
            try:
                date_obj = datetime.fromisoformat(activated_date.replace('Z', '+00:00'))
                formatted_date = date_obj.strftime("%B %d, %Y at %I:%M %p")
            except:
                formatted_date = activated_date
        else:
            formatted_date = activated_date
            
        info_text = f"Licensed on: {formatted_date}\nHardware ID: {license_info.get('hardware_id', 'Unknown')}"
        
        info_label = tk.Label(content_frame, text=info_text, font=("Segoe UI", 11),
                             fg='#64748b', bg='#f8fafc', justify=tk.CENTER)
        info_label.pack(pady=(0, 40))
        
        # Feature buttons frame
        features_frame = tk.Frame(content_frame, bg='#f8fafc')
        features_frame.pack(pady=20)
        
        # Sample feature buttons
        self.create_feature_button(features_frame, "📊 Analytics", self.feature_analytics, row=0, col=0)
        self.create_feature_button(features_frame, "⚙️ Settings", self.feature_settings, row=0, col=1)
        self.create_feature_button(features_frame, "📈 Reports", self.feature_reports, row=1, col=0)
        self.create_feature_button(features_frame, "🔧 Tools", self.feature_tools, row=1, col=1)
        
        # Status bar
        status_frame = tk.Frame(content_frame, bg='#e2e8f0', padx=20, pady=15)
        status_frame.pack(fill=tk.X, pady=(40, 0))
        
        status_text = tk.Label(status_frame, text="🟢 Software is running with valid license",
                              font=("Segoe UI", 10), fg='#059669', bg='#e2e8f0')
        status_text.pack(side=tk.LEFT)
        
        # About button
        about_btn = tk.Button(status_frame, text="About", command=self.show_about,
                             font=("Segoe UI", 9), bg='#6b7280', fg='white', relief=tk.FLAT,
                             cursor="hand2", width=8)
        about_btn.pack(side=tk.RIGHT)

    def create_feature_button(self, parent, text, command, row, col):
        """Create a feature button"""
        button = tk.Button(parent, text=text, command=command, font=("Segoe UI", 12, "bold"),
                          bg='#3b82f6', fg='white', relief=tk.FLAT, bd=0, cursor="hand2",
                          width=20, height=3, activebackground='#2563eb', activeforeground='white')
        button.grid(row=row, column=col, padx=15, pady=15)

    def feature_analytics(self):
        """Analytics feature"""
        messagebox.showinfo("📊 Analytics", "Analytics feature is now available!\nThis is where your analytics would go.")

    def feature_settings(self):
        """Settings feature"""
        messagebox.showinfo("⚙️ Settings", "Settings panel opened!\nConfigure your application here.")

    def feature_reports(self):
        """Reports feature"""
        messagebox.showinfo("📈 Reports", "Reports module loaded!\nGenerate and view reports here.")

    def feature_tools(self):
        """Tools feature"""
        messagebox.showinfo("🔧 Tools", "Tools panel opened!\nAccess various tools and utilities.")

    def show_about(self):
        """Show about dialog"""
        license_info = self.license_checker.check_license()[1]
        hwid = license_info.get('hardware_id', 'Unknown') if isinstance(license_info, dict) else 'Unknown'
        
        about_text = f"""My Software v1.0

Licensed Software
Hardware ID: {hwid}

© 2024 Your Company
All rights reserved."""
        
        messagebox.showinfo("About", about_text)

if __name__ == "__main__":
    root = tk.Tk()
    
    # Center window
    root.update_idletasks()
    width = 800
    height = 600
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    app = MainApplication(root)
    root.mainloop()
