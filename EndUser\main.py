#!/usr/bin/env python3
"""
Local Software Activation System - End User Application
Main application with local license verification
"""

import tkinter as tk
from tkinter import messagebox
import hashlib
import json
import os
import base64
import uuid
import platform
import subprocess

# Configuration
LICENSE_FILE = "license.key"
SECRET_KEY = "SecureKey2024!@#$%"  # Must match license generator

def get_hardware_id():
    """Generate unique hardware ID based on system information"""
    try:
        # Get system information
        system_info = platform.system() + platform.version() + platform.machine()
        
        # Get additional hardware info on Windows
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            except:
                pass
        
        # Add MAC address
        system_info += str(uuid.getnode())
        
        # Create hash and format
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        return formatted_id.upper()
    except:
        # Fallback to simple MAC address
        node = str(uuid.getnode())
        return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()

def create_signature(email, password, hwid):
    """Create SHA256 signature for verification"""
    data = f"{email}{password}{hwid}{SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()

def verify_license():
    """Verify if valid license exists"""
    if not os.path.exists(LICENSE_FILE):
        return False, "License file not found"
    
    try:
        # Read and decode license file
        with open(LICENSE_FILE, "r") as f:
            encoded_data = f.read().strip()
        
        decoded_data = base64.b64decode(encoded_data).decode()
        license_data = json.loads(decoded_data)
        
        # Extract license information
        stored_email = license_data.get("email", "")
        stored_hwid = license_data.get("hwid", "")
        stored_signature = license_data.get("signature", "")
        
        # Verify hardware ID
        current_hwid = get_hardware_id()
        if stored_hwid != current_hwid:
            return False, "Hardware ID mismatch"
        
        # Verify signature format
        if len(stored_signature) != 64:  # SHA256 length
            return False, "Invalid signature format"
        
        return True, f"Licensed to: {stored_email}"
        
    except Exception as e:
        return False, f"License verification failed: {str(e)}"

class SoftwareApp:
    def __init__(self, root):
        self.root = root
        self.root.title("My Software")
        self.root.geometry("500x450")
        self.root.resizable(False, False)
        self.root.configure(bg='#f0f0f0')
        
        # Center window
        self.center_window()
        
        # Check license status
        is_valid, message = verify_license()
        
        if is_valid:
            self.show_main_application(message)
        else:
            self.show_activation_screen(message)

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = 500
        height = 450
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def show_activation_screen(self, error_message):
        """Show activation screen when license is invalid"""
        self.clear_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=40, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="🔐 Software Activation Required", 
                              font=("Arial", 16, "bold"), bg='#f0f0f0', fg='#d32f2f')
        title_label.pack(pady=(0, 20))
        
        # Error message
        error_label = tk.Label(main_frame, text=f"Status: {error_message}", 
                              font=("Arial", 10), bg='#f0f0f0', fg='#666')
        error_label.pack(pady=(0, 30))
        
        # Activation form
        form_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1, padx=25, pady=25)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email field
        tk.Label(form_frame, text="Email Address:", font=("Arial", 11, "bold"), 
                bg='#ffffff', fg='#333').pack(anchor=tk.W, pady=(0, 5))
        
        self.email_entry = tk.Entry(form_frame, font=("Arial", 10), width=40, 
                                   relief=tk.SOLID, bd=1)
        self.email_entry.pack(pady=(0, 15), ipady=5)
        
        # Password field
        tk.Label(form_frame, text="Password:", font=("Arial", 11, "bold"), 
                bg='#ffffff', fg='#333').pack(anchor=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(form_frame, show="*", font=("Arial", 10), 
                                      width=40, relief=tk.SOLID, bd=1)
        self.password_entry.pack(pady=(0, 20), ipady=5)
        
        # Activate button
        activate_btn = tk.Button(form_frame, text="🚀 Request Activation", 
                               command=self.request_activation,
                               font=("Arial", 11, "bold"), bg='#1976d2', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=30, pady=8)
        activate_btn.pack(pady=(0, 10))
        
        # Hardware ID section
        hw_frame = tk.Frame(main_frame, bg='#e3f2fd', relief=tk.SOLID, bd=1, padx=20, pady=15)
        hw_frame.pack(fill=tk.X)
        
        tk.Label(hw_frame, text="📱 Your Hardware ID:", font=("Arial", 11, "bold"), 
                bg='#e3f2fd', fg='#1565c0').pack()
        
        hwid = get_hardware_id()
        self.hwid_var = tk.StringVar(value=hwid)
        
        hwid_entry = tk.Entry(hw_frame, textvariable=self.hwid_var, state="readonly",
                             font=("Courier", 10), bg='#ffffff', fg='#333',
                             relief=tk.SOLID, bd=1, justify=tk.CENTER, width=25)
        hwid_entry.pack(pady=(10, 5))
        
        # Copy button
        copy_btn = tk.Button(hw_frame, text="📋 Copy Hardware ID", 
                           command=self.copy_hwid,
                           font=("Arial", 9), bg='#4caf50', fg='white',
                           relief=tk.FLAT, cursor="hand2", width=20)
        copy_btn.pack(pady=(5, 0))
        
        # Instructions
        instructions = """📋 Activation Instructions:
1. Fill in your email and password above
2. Copy your Hardware ID using the button
3. Send these details to the software provider
4. You will receive a license.key file
5. Place the license.key file next to this program
6. Restart the software to activate"""
        
        tk.Label(hw_frame, text=instructions, font=("Arial", 8), 
                bg='#e3f2fd', fg='#424242', justify=tk.LEFT).pack(pady=(10, 0))

    def show_main_application(self, license_info):
        """Show main application when license is valid"""
        self.clear_window()
        self.root.title("My Software - Licensed")
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=40, pady=40)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Success header
        header_frame = tk.Frame(main_frame, bg='#e8f5e8', relief=tk.SOLID, bd=1, padx=20, pady=20)
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        tk.Label(header_frame, text="✅ Software Successfully Activated!", 
                font=("Arial", 16, "bold"), bg='#e8f5e8', fg='#2e7d32').pack()
        
        tk.Label(header_frame, text=license_info, 
                font=("Arial", 10), bg='#e8f5e8', fg='#388e3c').pack(pady=(5, 0))
        
        # Application content
        content_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1, padx=30, pady=30)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(content_frame, text="🚀 Welcome to Your Licensed Software!", 
                font=("Arial", 18, "bold"), bg='#ffffff', fg='#1976d2').pack(pady=(0, 20))
        
        tk.Label(content_frame, text="All features are now available for use.\nThank you for your purchase!", 
                font=("Arial", 12), bg='#ffffff', fg='#666', justify=tk.CENTER).pack(pady=(0, 30))
        
        # Feature buttons
        features_frame = tk.Frame(content_frame, bg='#ffffff')
        features_frame.pack(pady=20)
        
        tk.Button(features_frame, text="📊 Analytics", command=self.feature_analytics,
                 font=("Arial", 10), bg='#ff9800', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        tk.Button(features_frame, text="⚙️ Settings", command=self.feature_settings,
                 font=("Arial", 10), bg='#9c27b0', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        tk.Button(features_frame, text="📈 Reports", command=self.feature_reports,
                 font=("Arial", 10), bg='#00bcd4', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        # License details
        details_frame = tk.Frame(content_frame, bg='#f5f5f5', relief=tk.SOLID, bd=1, padx=15, pady=15)
        details_frame.pack(fill=tk.X, pady=(30, 0))
        
        try:
            with open(LICENSE_FILE, "r") as f:
                encoded_data = f.read().strip()
            decoded_data = base64.b64decode(encoded_data).decode()
            license_data = json.loads(decoded_data)
            
            details_text = f"License Details:\nEmail: {license_data.get('email', 'Unknown')}\nHardware ID: {license_data.get('hwid', 'Unknown')}"
            tk.Label(details_frame, text=details_text, font=("Arial", 9), 
                    bg='#f5f5f5', fg='#666', justify=tk.LEFT).pack()
        except:
            pass
        
        # Exit button
        tk.Button(content_frame, text="❌ Exit Application", command=self.root.quit,
                 font=("Arial", 10), bg='#f44336', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=20, pady=8).pack(pady=(20, 0))

    def request_activation(self):
        """Handle activation request"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not email or not password:
            messagebox.showerror("Error", "Please enter both email and password")
            return
        
        if "@" not in email:
            messagebox.showerror("Error", "Please enter a valid email address")
            return
        
        hwid = get_hardware_id()
        
        # Show activation details
        activation_info = f"""Activation Request Details:

📧 Email: {email}
🔑 Password: {password}
💻 Hardware ID: {hwid}

Please send these details to the software provider to receive your license.key file.

After receiving the license file:
1. Place it in the same folder as this program
2. Restart the software
3. The software will activate automatically"""
        
        messagebox.showinfo("Activation Request", activation_info)

    def copy_hwid(self):
        """Copy hardware ID to clipboard"""
        hwid = self.hwid_var.get()
        self.root.clipboard_clear()
        self.root.clipboard_append(hwid)
        messagebox.showinfo("Copied", f"Hardware ID copied to clipboard:\n{hwid}")

    def feature_analytics(self):
        """Sample analytics feature"""
        messagebox.showinfo("Analytics", "📊 Analytics module loaded!\nView your data insights here.")

    def feature_settings(self):
        """Sample settings feature"""
        messagebox.showinfo("Settings", "⚙️ Settings panel opened!\nConfigure your preferences here.")

    def feature_reports(self):
        """Sample reports feature"""
        messagebox.showinfo("Reports", "📈 Reports module loaded!\nGenerate and export reports here.")

    def clear_window(self):
        """Clear all widgets from window"""
        for widget in self.root.winfo_children():
            widget.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = SoftwareApp(root)
    root.mainloop()
