#!/usr/bin/env python3
"""
Create test license for current hardware
"""

import hashlib
import json
import base64
import uuid

# Configuration - Must match main.py
SECRET_KEY = "MySecretKey2024!@#"

def get_hardware_id():
    """Get unique hardware ID"""
    return str(uuid.getnode())

def create_signature(email, password, hwid):
    """Create SHA256 signature"""
    data = f"{email}{password}{hwid}{SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()

def create_test_license():
    """Create test license for current hardware"""
    # Test credentials
    email = "<EMAIL>"
    password = "test123"
    hwid = get_hardware_id()
    
    print(f"Creating test license for:")
    print(f"Email: {email}")
    print(f"Password: {password}")
    print(f"Hardware ID: {hwid}")
    
    # Create signature
    signature = create_signature(email, password, hwid)
    
    # Create license data
    license_data = {
        "email": email,
        "hwid": hwid,
        "signature": signature
    }
    
    # Encode to base64
    json_data = json.dumps(license_data)
    encoded_data = base64.b64encode(json_data.encode()).decode()
    
    # Save license file
    with open("license.key", "w") as f:
        f.write(encoded_data)
    
    print(f"\n✅ Test license created: license.key")
    print(f"📧 Use email: {email}")
    print(f"🔑 Use password: {password}")
    print(f"💻 Hardware ID: {hwid}")

if __name__ == "__main__":
    create_test_license()
