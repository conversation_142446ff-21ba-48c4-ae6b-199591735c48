#!/usr/bin/env python3
"""
Create test license for current hardware
"""

import hashlib
import json
import base64
import uuid
import platform
import subprocess
from datetime import datetime

# Configuration - Must match main.py
SECRET_KEY = "SecureKey2024!@#$%"

def get_hardware_id():
    """Generate unique hardware ID based on system information"""
    try:
        # Get system information
        system_info = platform.system() + platform.version() + platform.machine()

        # Get additional hardware info on Windows
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            except:
                pass

        # Add MAC address
        system_info += str(uuid.getnode())

        # Create hash and format
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        return formatted_id.upper()
    except:
        # Fallback to simple MAC address
        node = str(uuid.getnode())
        return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()

def create_signature(email, password, hwid):
    """Create SHA256 signature for license verification"""
    data = f"{email}{password}{hwid}{SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()

def create_test_license():
    """Create test license for current hardware"""
    # Test credentials
    email = "<EMAIL>"
    password = "test123"
    hwid = get_hardware_id()

    print(f"Creating test license for:")
    print(f"📧 Email: {email}")
    print(f"🔑 Password: {password}")
    print(f"💻 Hardware ID: {hwid}")

    # Create signature
    signature = create_signature(email, password, hwid)

    # Create license data
    license_data = {
        "email": email,
        "hwid": hwid,
        "signature": signature,
        "created_date": datetime.now().isoformat(),
        "version": "1.0"
    }

    # Convert to JSON and encode with base64
    json_data = json.dumps(license_data, indent=None, separators=(',', ':'))
    encoded_data = base64.b64encode(json_data.encode()).decode()

    # Save license file in EndUser directory
    license_path = "EndUser/license.key"
    with open(license_path, "w") as f:
        f.write(encoded_data)

    print(f"\n✅ Test license created: {license_path}")
    print(f"📧 Use email: {email}")
    print(f"🔑 Use password: {password}")
    print(f"💻 Hardware ID: {hwid}")
    print(f"\nNow run the main program to test activation!")

if __name__ == "__main__":
    create_test_license()
