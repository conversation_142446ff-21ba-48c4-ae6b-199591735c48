# 🔑 دليل المطور - أداة توليد التراخيص

## 📋 **نظرة عامة:**
أداة توليد التراخيص هي تطبيق مستقل للمطورين لإنشاء ملفات `license.key` للعملاء. الأداة تحتوي على واجهة رسومية سهلة الاستخدام مع ميزات متقدمة.

## 🚀 **تشغيل الأداة:**

### **الطريقة الأولى: Python**
```bash
python license_generator.py
```

### **الطريقة الثانية: ملف تنفيذي**
```bash
./LicenseGenerator.exe
```

## 🎯 **الميزات الرئيسية:**

### **1. توليد التراخيص:**
- إدخال بيانات العميل (Email, Password, Hardware ID)
- توليد ملف `license.key` مشفر
- حفظ الملف بأسماء مخصصة
- التحقق من صحة البيانات المدخلة

### **2. التحقق من التراخيص:**
- فتح وفحص ملفات التراخيص الموجودة
- عرض تفاصيل الترخيص
- التأكد من سلامة الملف

### **3. إدارة السجلات:**
- تتبع جميع التراخيص المُنشأة
- عرض تاريخ الإنشاء
- سجل العملاء والملفات

### **4. أدوات مساعدة:**
- قوالب جاهزة للاختبار
- مسح الحقول
- حفظ في مواقع مخصصة

## 📝 **خطوات إنشاء ترخيص:**

### **الخطوة 1: استلام بيانات العميل**
العميل يرسل لك:
- 📧 البريد الإلكتروني
- 🔑 كلمة المرور المطلوبة
- 💻 Hardware ID (من البرنامج)

### **الخطوة 2: إدخال البيانات**
1. افتح أداة توليد التراخيص
2. أدخل البيانات في الحقول المناسبة:
   - **Customer Email**: البريد الإلكتروني
   - **Customer Password**: كلمة المرور
   - **Customer Hardware ID**: معرف الجهاز

### **الخطوة 3: توليد الترخيص**
1. اضغط "🚀 Generate License" للحفظ باسم `license.key`
2. أو اضغط "💾 Save As..." للحفظ باسم مخصص

### **الخطوة 4: إرسال الترخيص**
- أرسل ملف `license.key` للعميل
- أخبر العميل بوضع الملف مع البرنامج
- العميل يعيد تشغيل البرنامج للتفعيل

## 🔧 **الأدوات المتقدمة:**

### **التحقق من الترخيص:**
```
🔍 Verify License → اختر ملف license.key → عرض التفاصيل
```

### **استخدام القوالب:**
```
📋 Load Template → يملأ الحقول ببيانات تجريبية
```

### **عرض السجلات:**
```
📊 Show History → عرض جميع التراخيص المُنشأة
```

## 🛡️ **الأمان والحماية:**

### **التشفير:**
- استخدام SHA256 للتوقيع الرقمي
- تشفير Base64 للملف
- مفتاح سري مشترك مع البرنامج الرئيسي

### **الحماية:**
- ربط الترخيص بـ Hardware ID محدد
- لا يمكن استخدام الترخيص على أجهزة أخرى
- التحقق من سلامة البيانات

## ⚙️ **الإعدادات المتقدمة:**

### **تغيير المفتاح السري:**
في ملف `license_generator.py`:
```python
SECRET_KEY = "YourNewSecretKey2024!@#$%"
```
**⚠️ يجب تغيير نفس المفتاح في البرنامج الرئيسي**

### **تخصيص تنسيق الملف:**
يمكن تعديل هيكل بيانات الترخيص في دالة `generate_license_file()`

## 📊 **هيكل ملف الترخيص:**
```json
{
  "email": "<EMAIL>",
  "hwid": "ABCD-1234-EFGH-5678", 
  "signature": "sha256_hash_signature",
  "created_date": "2024-01-01T12:00:00",
  "version": "1.0"
}
```

## 🔍 **استكشاف الأخطاء:**

### **مشاكل شائعة:**
- **"Error creating license"**: تحقق من صحة البيانات المدخلة
- **"Invalid email format"**: تأكد من وجود @ في البريد
- **"Hardware ID too short"**: تأكد من Hardware ID الصحيح

### **نصائح:**
- احتفظ بنسخة من كل ترخيص تنشئه
- سجل بيانات العملاء للمرجع
- اختبر الترخيص قبل الإرسال

## 📁 **إدارة الملفات:**

### **تنظيم التراخيص:**
```
📁 Licenses/
├── customer1_license.key
├── customer2_license.key
└── backup/
    ├── license_backup_2024-01.key
    └── license_backup_2024-02.key
```

### **النسخ الاحتياطي:**
- احتفظ بنسخة من كل ترخيص
- سجل تاريخ الإنشاء والعميل
- استخدم أسماء ملفات واضحة

## 🎯 **أفضل الممارسات:**

### **للأمان:**
- لا تشارك المفتاح السري مع أحد
- احتفظ بأداة التوليد في مكان آمن
- غير المفتاح السري دورياً

### **للإدارة:**
- استخدم أسماء ملفات واضحة
- احتفظ بسجل العملاء
- اختبر كل ترخيص قبل الإرسال

### **للدعم:**
- احتفظ بنسخة من كل ترخيص
- سجل Hardware ID لكل عميل
- وثق أي مشاكل وحلولها

---
**أداة احترافية لإدارة تراخيص البرامج! 🔑**
