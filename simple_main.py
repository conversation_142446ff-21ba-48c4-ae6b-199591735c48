import tkinter as tk
from tkinter import messagebox
import hashlib
import json
import os
import base64
from cryptography.fernet import <PERSON>rnet
import uuid
import webbrowser
import pyperclip
from PIL import Image, ImageTk

# ملفات النظام
USERS_FILE = "users.enc"
LICENSE_FILE = "license.key"
SECRET_KEY = b'4ibKLILdcFR7E--NU8rH2eFBf90nOSDwwjnHBk2RO00='  # مفتاح مدمج

# دالة جلب Hardware ID
def get_hardware_id():
    """Get unique hardware ID"""
    try:
        import platform
        import subprocess
        
        # Get system info
        system_info = platform.system() + platform.version() + platform.machine()
        
        # Get UUID from system
        if platform.system() == "Windows":
            result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
            uuid_result = result.split('\n')[1].strip()
            system_info += uuid_result
        else:
            system_info += str(uuid.getnode())
        
        # Create hash
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        return formatted_id.upper()
    except:
        # Fallback
        node = str(uuid.getnode())
        return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()

# دالة التحقق من الرخصة
def is_activated():
    """Check if software is activated"""
    if not os.path.exists(LICENSE_FILE):
        return False
    try:
        with open(LICENSE_FILE, "r") as f:
            saved = f.read().strip()
        return saved == get_hardware_id()
    except:
        return False

# فك تشفير ملف المستخدمين
def load_users():
    """Load and decrypt users from encrypted file"""
    if not os.path.exists(USERS_FILE):
        return []

    try:
        fernet = Fernet(SECRET_KEY)
        with open(USERS_FILE, "rb") as uf:
            encrypted_data = uf.read()
        
        decrypted_data = fernet.decrypt(encrypted_data).decode()
        return json.loads(decrypted_data)
    except:
        return []

# دالة إنشاء ملف التفعيل
def save_license():
    """Save license file"""
    try:
        with open(LICENSE_FILE, "w") as f:
            f.write(get_hardware_id())
        return True
    except:
        return False

# واجهة البرنامج
class LoginApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Software Activation")
        self.root.geometry("500x650")
        self.root.resizable(False, False)
        
        # Set icon
        try:
            self.root.iconbitmap("logo.ico")
        except:
            pass
        
        # Colors
        self.colors = {
            'bg_primary': '#1e293b',
            'bg_secondary': '#334155', 
            'bg_light': '#f8fafc',
            'accent': '#3b82f6',
            'text_primary': '#ffffff',
            'text_secondary': '#94a3b8',
            'success': '#10b981'
        }
        
        self.root.configure(bg=self.colors['bg_primary'])

        # Check activation status
        if is_activated():
            self.show_main_app()
        else:
            self.show_login()

    def show_login(self):
        """Show login/activation interface"""
        self.clear_window()
        
        # Main container
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], padx=30, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Logo
        try:
            logo_image = Image.open("logo.ico")
            logo_image = logo_image.resize((80, 80), Image.Resampling.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_image)
            
            logo_label = tk.Label(main_frame, image=self.logo_photo, bg=self.colors['bg_primary'])
            logo_label.pack(pady=(0, 15))
        except:
            pass
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="SOFTWARE ACTIVATION",
            font=("Segoe UI", 18, "bold"),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_primary']
        )
        title_label.pack(pady=(0, 25))
        
        # Login form
        form_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], padx=25, pady=25)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email
        tk.Label(form_frame, text="Email:", font=("Segoe UI", 11, "bold"),
                fg=self.colors['text_primary'], bg=self.colors['bg_secondary']).pack(anchor=tk.W, pady=(0, 5))
        
        self.email_entry = tk.Entry(form_frame, font=("Segoe UI", 11),
                                   bg=self.colors['bg_light'], fg='#1e293b', relief=tk.FLAT, bd=0,
                                   highlightthickness=2, highlightcolor=self.colors['accent'])
        self.email_entry.pack(fill=tk.X, pady=(0, 15), ipady=8)

        # Password
        tk.Label(form_frame, text="Password:", font=("Segoe UI", 11, "bold"),
                fg=self.colors['text_primary'], bg=self.colors['bg_secondary']).pack(anchor=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(form_frame, show="*", font=("Segoe UI", 11),
                                      bg=self.colors['bg_light'], fg='#1e293b', relief=tk.FLAT, bd=0,
                                      highlightthickness=2, highlightcolor=self.colors['accent'])
        self.password_entry.pack(fill=tk.X, pady=(0, 20), ipady=8)

        # Login button
        tk.Button(form_frame, text="ACTIVATE SOFTWARE", command=self.check_login,
                 font=("Segoe UI", 12, "bold"), bg=self.colors['accent'], fg=self.colors['text_primary'],
                 relief=tk.FLAT, bd=0, cursor="hand2", activebackground='#2563eb',
                 activeforeground=self.colors['text_primary']).pack(fill=tk.X, pady=(0, 10), ipady=10)

        # Hardware ID section
        hw_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], padx=25, pady=20)
        hw_frame.pack(fill=tk.X, pady=(0, 20))
        
        tk.Label(hw_frame, text="Hardware ID", font=("Segoe UI", 12, "bold"),
                fg=self.colors['text_primary'], bg=self.colors['bg_secondary']).pack(pady=(0, 5))
        
        tk.Label(hw_frame, text="Send this ID to get activation", font=("Segoe UI", 9),
                fg=self.colors['text_secondary'], bg=self.colors['bg_secondary']).pack(pady=(0, 10))
        
        # Hardware ID display
        hw_id_frame = tk.Frame(hw_frame, bg=self.colors['bg_secondary'])
        hw_id_frame.pack(fill=tk.X)
        
        hwid = get_hardware_id()
        self.hw_id_var = tk.StringVar(value=hwid)
        
        hw_entry = tk.Entry(hw_id_frame, textvariable=self.hw_id_var, state="readonly",
                           font=("Consolas", 10), bg=self.colors['bg_light'], fg='#1e293b',
                           relief=tk.FLAT, bd=0, justify=tk.CENTER)
        hw_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5)
        
        tk.Button(hw_id_frame, text="COPY", command=lambda: self.copy_hwid(hwid),
                 font=("Segoe UI", 9, "bold"), bg=self.colors['success'], fg=self.colors['text_primary'],
                 relief=tk.FLAT, bd=0, cursor="hand2", width=8).pack(side=tk.RIGHT, padx=(10, 0), ipady=5)

        # Contact section
        self.create_contact_section(main_frame)

    def create_contact_section(self, parent):
        """Create contact section"""
        social_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], padx=25, pady=20)
        social_frame.pack(fill=tk.X, pady=(10, 0))
        
        tk.Label(social_frame, text="Contact Us", font=("Segoe UI", 12, "bold"),
                fg=self.colors['text_primary'], bg=self.colors['bg_secondary']).pack(pady=(0, 15))
        
        buttons_frame = tk.Frame(social_frame, bg=self.colors['bg_secondary'])
        buttons_frame.pack()
        
        # Social buttons
        tk.Button(buttons_frame, text="📱 WhatsApp", command=lambda: webbrowser.open("https://wa.me/201200578402"),
                 font=("Segoe UI", 9, "bold"), bg="#25D366", fg="white", relief=tk.FLAT, bd=0,
                 cursor="hand2", width=12, activebackground="#1DA851").pack(side=tk.LEFT, padx=5, ipady=6)
        
        tk.Button(buttons_frame, text="📘 Facebook", command=lambda: webbrowser.open("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v"),
                 font=("Segoe UI", 9, "bold"), bg="#1877f2", fg="white", relief=tk.FLAT, bd=0,
                 cursor="hand2", width=12, activebackground="#166fe5").pack(side=tk.LEFT, padx=5, ipady=6)
        
        tk.Button(buttons_frame, text="✈️ Telegram", command=lambda: webbrowser.open("http://t.me/Mohamed_Abdo26"),
                 font=("Segoe UI", 9, "bold"), bg="#0088cc", fg="white", relief=tk.FLAT, bd=0,
                 cursor="hand2", width=12, activebackground="#006699").pack(side=tk.LEFT, padx=5, ipady=6)

    def copy_hwid(self, hwid):
        """Copy hardware ID to clipboard"""
        pyperclip.copy(hwid)
        messagebox.showinfo("Copied", "Hardware ID copied to clipboard!")

    def show_main_app(self):
        """Show main application after activation"""
        self.clear_window()
        self.root.title("My Software - Activated")
        self.root.geometry("700x500")
        
        # Main app interface
        main_frame = tk.Frame(self.root, bg='#f8fafc', padx=40, pady=40)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Success message
        tk.Label(main_frame, text="🎉 SOFTWARE ACTIVATED SUCCESSFULLY! 🎉",
                font=("Segoe UI", 20, "bold"), fg='#10b981', bg='#f8fafc').pack(pady=(0, 30))
        
        # Info
        tk.Label(main_frame, text="Your software is now fully activated and ready to use.\nThis activation is permanent for this device.",
                font=("Segoe UI", 12), fg='#374151', bg='#f8fafc', justify=tk.CENTER).pack(pady=(0, 30))
        
        # Hardware ID info
        tk.Label(main_frame, text=f"Licensed Hardware ID: {get_hardware_id()}",
                font=("Consolas", 10), fg='#6b7280', bg='#f8fafc').pack(pady=(0, 30))
        
        # Main application content
        content_frame = tk.Frame(main_frame, bg='#e2e8f0', padx=30, pady=30)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(content_frame, text="🚀 YOUR MAIN APPLICATION CONTENT HERE 🚀",
                font=("Segoe UI", 16, "bold"), fg='#3b82f6', bg='#e2e8f0').pack(pady=20)
        
        # Sample buttons
        buttons_frame = tk.Frame(content_frame, bg='#e2e8f0')
        buttons_frame.pack(pady=20)
        
        tk.Button(buttons_frame, text="Feature 1", command=lambda: messagebox.showinfo("Feature 1", "This is Feature 1!"),
                 font=("Segoe UI", 10), bg='#3b82f6', fg='white', relief=tk.FLAT, cursor="hand2",
                 width=15).pack(side=tk.LEFT, padx=10)
        
        tk.Button(buttons_frame, text="Feature 2", command=lambda: messagebox.showinfo("Feature 2", "This is Feature 2!"),
                 font=("Segoe UI", 10), bg='#10b981', fg='white', relief=tk.FLAT, cursor="hand2",
                 width=15).pack(side=tk.LEFT, padx=10)
        
        tk.Button(buttons_frame, text="Settings", command=self.show_settings,
                 font=("Segoe UI", 10), bg='#6b7280', fg='white', relief=tk.FLAT, cursor="hand2",
                 width=15).pack(side=tk.LEFT, padx=10)

    def show_settings(self):
        """Show settings window"""
        settings = tk.Toplevel(self.root)
        settings.title("Settings")
        settings.geometry("400x300")
        settings.configure(bg='#f8fafc')
        
        tk.Label(settings, text="⚙️ Settings", font=("Segoe UI", 16, "bold"),
                fg='#1e293b', bg='#f8fafc').pack(pady=20)
        
        tk.Label(settings, text=f"Hardware ID: {get_hardware_id()}\nActivation Status: ✅ Active",
                font=("Segoe UI", 10), fg='#64748b', bg='#f8fafc', justify=tk.LEFT).pack(pady=20)
        
        tk.Button(settings, text="Close", command=settings.destroy,
                 font=("Segoe UI", 10), bg='#6b7280', fg='white', relief=tk.FLAT, cursor="hand2").pack(pady=20)

    def check_login(self):
        """Check login credentials and activate"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not email or not password:
            messagebox.showerror("Error", "Please enter both email and password")
            return
        
        users = load_users()
        current_hwid = get_hardware_id()
        
        for user in users:
            if (user["email"].lower() == email.lower() and 
                user["password"] == password and 
                user["hwid"] == current_hwid):
                
                if save_license():
                    messagebox.showinfo("Success", "Software activated successfully!")
                    self.show_main_app()
                    return
                else:
                    messagebox.showerror("Error", "Failed to create license file")
                    return

        messagebox.showerror("Error", "Invalid credentials or hardware ID not authorized")

    def clear_window(self):
        """Clear all widgets from window"""
        for widget in self.root.winfo_children():
            widget.destroy()

# تشغيل التطبيق
if __name__ == "__main__":
    root = tk.Tk()
    
    # Center window
    root.update_idletasks()
    width = 500
    height = 650
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    app = LoginApp(root)
    root.mainloop()
