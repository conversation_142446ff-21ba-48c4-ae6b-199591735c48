#!/usr/bin/env python3
"""
Script to encrypt users.json file for secure distribution
This script should be run by the developer only
"""

import json
import os
from cryptography.fernet import Fernet

def generate_key():
    """Generate a new encryption key"""
    key = Fernet.generate_key()
    with open('secret.key', 'wb') as key_file:
        key_file.write(key)
    print("✅ Secret key generated and saved to 'secret.key'")
    return key

def load_key():
    """Load the encryption key"""
    try:
        with open('secret.key', 'rb') as key_file:
            return key_file.read()
    except FileNotFoundError:
        print("❌ Secret key not found. Generating new key...")
        return generate_key()

def encrypt_users_file():
    """Encrypt users.json to users.enc"""
    try:
        # Load the encryption key
        key = load_key()
        fernet = Fernet(key)
        
        # Read users.json
        with open('users.json', 'r', encoding='utf-8') as file:
            users_data = file.read()
        
        # Encrypt the data
        encrypted_data = fernet.encrypt(users_data.encode())
        
        # Save encrypted data
        with open('users.enc', 'wb') as encrypted_file:
            encrypted_file.write(encrypted_data)
        
        print("✅ Users file encrypted successfully!")
        print("📁 Files created:")
        print("   - users.enc (distribute this)")
        print("   - secret.key (keep this secret)")
        
    except FileNotFoundError:
        print("❌ users.json file not found!")
        print("Please create users.json first with the following format:")
        print("""
[
  {
    "email": "<EMAIL>",
    "password": "123456",
    "hwid": "ABCD-1234-EFGH-5678"
  }
]
        """)
    except Exception as e:
        print(f"❌ Error: {e}")

def decrypt_users_file():
    """Decrypt users.enc for testing (developer only)"""
    try:
        key = load_key()
        fernet = Fernet(key)
        
        with open('users.enc', 'rb') as encrypted_file:
            encrypted_data = encrypted_file.read()
        
        decrypted_data = fernet.decrypt(encrypted_data)
        users = json.loads(decrypted_data.decode())
        
        print("✅ Decrypted users data:")
        for user in users:
            print(f"   Email: {user['email']}")
            print(f"   Password: {user['password']}")
            print(f"   HWID: {user['hwid']}")
            print("   ---")
            
    except Exception as e:
        print(f"❌ Error decrypting: {e}")

if __name__ == "__main__":
    print("🔐 User Data Encryption Tool")
    print("=" * 40)
    
    while True:
        print("\nChoose an option:")
        print("1. Encrypt users.json → users.enc")
        print("2. Test decrypt users.enc")
        print("3. Generate new secret key")
        print("4. Exit")
        
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "1":
            encrypt_users_file()
        elif choice == "2":
            decrypt_users_file()
        elif choice == "3":
            generate_key()
        elif choice == "4":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice!")
